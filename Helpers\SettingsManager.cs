using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace DateFactoryApp.Helpers
{
    public class AppSettings
    {
        public string CompanyName { get; set; } = "شركة التمور المتقدمة";
        public string CompanyAddress { get; set; } = "الرياض - المملكة العربية السعودية";
        public string CompanyPhone { get; set; } = "+966 50 123 4567";
        public string CompanyEmail { get; set; } = "<EMAIL>";
        public string CompanyWebsite { get; set; } = "www.datefactory.com";
        public string TaxNumber { get; set; } = "";
        public string CommercialRegister { get; set; } = "";

        // UI Settings
        public bool IsDarkTheme { get; set; } = false;
        public string Language { get; set; } = "ar-SA";
        public bool ShowNotifications { get; set; } = true;
        public bool PlaySounds { get; set; } = true;
        public int NotificationDuration { get; set; } = 5; // seconds

        // Business Settings
        public decimal DefaultTaxRate { get; set; } = 0.15m; // 15% VAT
        public bool AutoCalculateTax { get; set; } = true;
        public bool AllowNegativeStock { get; set; } = false;
        public bool RequireCustomerForSale { get; set; } = false;
        public int LowStockThreshold { get; set; } = 10;
        public string DefaultCurrency { get; set; } = "SAR";
        public string CurrencySymbol { get; set; } = "ر.س";

        // Backup Settings
        public bool AutoBackupEnabled { get; set; } = true;
        public int AutoBackupIntervalHours { get; set; } = 24;
        public int MaxBackupFiles { get; set; } = 10;
        public string BackupPath { get; set; } = "Backups";

        // Sync Settings
        public bool AutoSyncEnabled { get; set; } = false;
        public string SyncEndpoint { get; set; } = "";
        public string SyncApiKey { get; set; } = "";
        public int SyncIntervalMinutes { get; set; } = 15;

        // Printing Settings
        public string DefaultPrinter { get; set; } = "";
        public bool AutoPrintInvoices { get; set; } = false;
        public bool AutoPrintReceipts { get; set; } = false;
        public string InvoiceTemplate { get; set; } = "Default";
        public string ReceiptTemplate { get; set; } = "Default";

        // Security Settings
        public bool RequirePasswordChange { get; set; } = false;
        public int PasswordExpiryDays { get; set; } = 90;
        public int MaxLoginAttempts { get; set; } = 5;
        public int LockoutDurationMinutes { get; set; } = 30;
        public bool EnableAuditLog { get; set; } = true;

        // Report Settings
        public string DefaultReportFormat { get; set; } = "PDF";
        public bool IncludeCompanyLogo { get; set; } = true;
        public bool ShowDetailedReports { get; set; } = true;
        public string ReportDateFormat { get; set; } = "yyyy/MM/dd";

        // Performance Settings
        public int PageSize { get; set; } = 50;
        public bool EnableCaching { get; set; } = true;
        public int CacheExpiryMinutes { get; set; } = 30;
        public bool LazyLoadData { get; set; } = true;
    }

    public class SettingsManager
    {
        private static SettingsManager? _instance;
        private static readonly object _lock = new object();
        private AppSettings _settings;
        private readonly string _settingsFilePath;

        public static SettingsManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new SettingsManager();
                    }
                }
                return _instance;
            }
        }

        public AppSettings Settings => _settings;

        private SettingsManager()
        {
            _settingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
            _settings = new AppSettings();
            LoadSettings();
        }

        public void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    var loadedSettings = JsonSerializer.Deserialize<AppSettings>(json);
                    if (loadedSettings != null)
                    {
                        _settings = loadedSettings;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error and use default settings
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
                _settings = new AppSettings();
            }
        }

        public async Task SaveSettingsAsync()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                await File.WriteAllTextAsync(_settingsFilePath, json);
                
                NotificationService.Success("تم حفظ الإعدادات بنجاح");
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في حفظ الإعدادات", ex.Message);
            }
        }

        public void SaveSettings()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                File.WriteAllText(_settingsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        public void ResetToDefaults()
        {
            _settings = new AppSettings();
            SaveSettings();
            NotificationService.Info("تم إعادة تعيين الإعدادات إلى القيم الافتراضية");
        }

        // Convenience methods for common settings
        public string GetCompanyInfo()
        {
            return $"{_settings.CompanyName}\n{_settings.CompanyAddress}\nهاتف: {_settings.CompanyPhone}";
        }

        public void UpdateCompanyInfo(string name, string address, string phone, string email)
        {
            _settings.CompanyName = name;
            _settings.CompanyAddress = address;
            _settings.CompanyPhone = phone;
            _settings.CompanyEmail = email;
            SaveSettings();
        }

        public void UpdateTheme(bool isDark)
        {
            _settings.IsDarkTheme = isDark;
            SaveSettings();
        }

        public void UpdateTaxSettings(decimal taxRate, bool autoCalculate)
        {
            _settings.DefaultTaxRate = taxRate;
            _settings.AutoCalculateTax = autoCalculate;
            SaveSettings();
        }

        public void UpdateBackupSettings(bool enabled, int intervalHours, int maxFiles)
        {
            _settings.AutoBackupEnabled = enabled;
            _settings.AutoBackupIntervalHours = intervalHours;
            _settings.MaxBackupFiles = maxFiles;
            SaveSettings();
        }

        public void UpdateSyncSettings(bool enabled, string endpoint, string apiKey, int intervalMinutes)
        {
            _settings.AutoSyncEnabled = enabled;
            _settings.SyncEndpoint = endpoint;
            _settings.SyncApiKey = apiKey;
            _settings.SyncIntervalMinutes = intervalMinutes;
            SaveSettings();
        }

        public void UpdateSecuritySettings(bool requirePasswordChange, int passwordExpiryDays, int maxLoginAttempts)
        {
            _settings.RequirePasswordChange = requirePasswordChange;
            _settings.PasswordExpiryDays = passwordExpiryDays;
            _settings.MaxLoginAttempts = maxLoginAttempts;
            SaveSettings();
        }

        // Event for settings changed
        public event EventHandler? SettingsChanged;

        protected virtual void OnSettingsChanged()
        {
            SettingsChanged?.Invoke(this, EventArgs.Empty);
        }

        // Import/Export settings
        public async Task<bool> ExportSettingsAsync(string filePath)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                await File.WriteAllTextAsync(filePath, json);
                
                NotificationService.Success("تم تصدير الإعدادات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في تصدير الإعدادات", ex.Message);
                return false;
            }
        }

        public async Task<bool> ImportSettingsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    NotificationService.Error("الملف غير موجود");
                    return false;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var importedSettings = JsonSerializer.Deserialize<AppSettings>(json);
                
                if (importedSettings != null)
                {
                    _settings = importedSettings;
                    await SaveSettingsAsync();
                    OnSettingsChanged();
                    
                    NotificationService.Success("تم استيراد الإعدادات بنجاح");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في استيراد الإعدادات", ex.Message);
                return false;
            }
        }
    }
}
