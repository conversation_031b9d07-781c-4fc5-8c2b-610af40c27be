using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using DateFactoryApp.Models;

namespace DateFactoryApp.Helpers
{
    public enum ExportFormat
    {
        CSV,
        Excel,
        PDF,
        JSON
    }

    public class ExportService
    {
        public static async Task<bool> ExportDataAsync<T>(IEnumerable<T> data, string fileName, ExportFormat format)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    FileName = fileName,
                    Filter = GetFileFilter(format),
                    DefaultExt = GetDefaultExtension(format)
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var filePath = saveFileDialog.FileName;
                    
                    switch (format)
                    {
                        case ExportFormat.CSV:
                            await ExportToCsvAsync(data, filePath);
                            break;
                        case ExportFormat.Excel:
                            await ExportToExcelAsync(data, filePath);
                            break;
                        case ExportFormat.PDF:
                            await ExportToPdfAsync(data, filePath);
                            break;
                        case ExportFormat.JSON:
                            await ExportToJsonAsync(data, filePath);
                            break;
                    }

                    NotificationService.Success("تم التصدير بنجاح", $"تم حفظ الملف في: {filePath}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في التصدير", ex.Message);
            }

            return false;
        }

        private static async Task ExportToCsvAsync<T>(IEnumerable<T> data, string filePath)
        {
            var csv = new StringBuilder();
            var properties = typeof(T).GetProperties();

            // Add headers
            var headers = properties.Select(p => GetDisplayName(p.Name)).ToArray();
            csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

            // Add data rows
            foreach (var item in data)
            {
                var values = properties.Select(p => 
                {
                    var value = p.GetValue(item);
                    return FormatValue(value);
                }).ToArray();
                
                csv.AppendLine(string.Join(",", values.Select(v => $"\"{v}\"")));
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
        }

        private static async Task ExportToExcelAsync<T>(IEnumerable<T> data, string filePath)
        {
            // For now, export as CSV with .xlsx extension
            // In a real implementation, you would use a library like EPPlus or ClosedXML
            await ExportToCsvAsync(data, filePath);
        }

        private static async Task ExportToPdfAsync<T>(IEnumerable<T> data, string filePath)
        {
            // For now, create a simple text-based PDF
            // In a real implementation, you would use a library like iTextSharp or PdfSharp
            var content = new StringBuilder();
            content.AppendLine("تقرير البيانات");
            content.AppendLine("================");
            content.AppendLine($"تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            content.AppendLine();

            var properties = typeof(T).GetProperties();
            
            foreach (var item in data)
            {
                foreach (var prop in properties)
                {
                    var value = prop.GetValue(item);
                    content.AppendLine($"{GetDisplayName(prop.Name)}: {FormatValue(value)}");
                }
                content.AppendLine("---");
            }

            await File.WriteAllTextAsync(filePath, content.ToString(), Encoding.UTF8);
        }

        private static async Task ExportToJsonAsync<T>(IEnumerable<T> data, string filePath)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
        }

        private static string GetFileFilter(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                ExportFormat.Excel => "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                ExportFormat.PDF => "PDF Files (*.pdf)|*.pdf|All Files (*.*)|*.*",
                ExportFormat.JSON => "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                _ => "All Files (*.*)|*.*"
            };
        }

        private static string GetDefaultExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => ".csv",
                ExportFormat.Excel => ".xlsx",
                ExportFormat.PDF => ".pdf",
                ExportFormat.JSON => ".json",
                _ => ".txt"
            };
        }

        private static string GetDisplayName(string propertyName)
        {
            // Convert property names to Arabic display names
            return propertyName switch
            {
                "Id" => "المعرف",
                "Name" => "الاسم",
                "Code" => "الكود",
                "Description" => "الوصف",
                "Price" => "السعر",
                "SalePrice" => "سعر البيع",
                "PurchasePrice" => "سعر الشراء",
                "WholesalePrice" => "سعر الجملة",
                "CurrentStock" => "المخزون الحالي",
                "MinStockLevel" => "الحد الأدنى للمخزون",
                "MaxStockLevel" => "الحد الأقصى للمخزون",
                "CreatedAt" => "تاريخ الإنشاء",
                "UpdatedAt" => "تاريخ التحديث",
                "IsActive" => "نشط",
                "Email" => "البريد الإلكتروني",
                "Phone" => "الهاتف",
                "Address" => "العنوان",
                "TotalAmount" => "المبلغ الإجمالي",
                "PaidAmount" => "المبلغ المدفوع",
                "RemainingAmount" => "المبلغ المتبقي",
                "InvoiceNumber" => "رقم الفاتورة",
                "SaleDate" => "تاريخ البيع",
                "CustomerName" => "اسم العميل",
                "ProductName" => "اسم المنتج",
                "Quantity" => "الكمية",
                "UnitPrice" => "سعر الوحدة",
                "LineTotal" => "إجمالي السطر",
                "Category" => "التصنيف",
                "Type" => "النوع",
                "Quality" => "الجودة",
                "PackageSize" => "حجم العبوة",
                _ => propertyName
            };
        }

        private static string FormatValue(object? value)
        {
            if (value == null)
                return "";

            return value switch
            {
                DateTime dateTime => dateTime.ToString("yyyy/MM/dd HH:mm"),
                decimal decimalValue => decimalValue.ToString("N2"),
                double doubleValue => doubleValue.ToString("N2"),
                float floatValue => floatValue.ToString("N2"),
                bool boolValue => boolValue ? "نعم" : "لا",
                Enum enumValue => GetEnumDisplayName(enumValue),
                _ => value.ToString() ?? ""
            };
        }

        private static string GetEnumDisplayName(Enum enumValue)
        {
            return enumValue switch
            {
                UserRole.Manager => "مدير",
                UserRole.Supervisor => "مشرف",
                UserRole.Cashier => "كاشير",
                UserRole.Worker => "عامل",
                
                ProductType.RawDates => "تمر خام",
                ProductType.ProcessedDates => "تمر معالج",
                ProductType.PackagedDates => "تمر معبأ",
                ProductType.DatePaste => "معجون تمر",
                ProductType.DateSyrup => "دبس تمر",
                ProductType.DatePowder => "مسحوق تمر",
                
                DateQuality.Premium => "ممتاز",
                DateQuality.Good => "جيد",
                DateQuality.Standard => "عادي",
                DateQuality.Economy => "اقتصادي",
                
                PaymentMethod.Cash => "نقدي",
                PaymentMethod.Credit => "آجل",
                PaymentMethod.Card => "بطاقة",
                PaymentMethod.Transfer => "تحويل",
                PaymentMethod.Check => "شيك",
                
                SaleStatus.Draft => "مسودة",
                SaleStatus.Completed => "مكتملة",
                SaleStatus.Cancelled => "ملغية",
                SaleStatus.Returned => "مرتجعة",
                
                _ => enumValue.ToString()
            };
        }

        // Quick export methods
        public static async Task<bool> ExportProductsToCsvAsync(IEnumerable<Product> products)
        {
            return await ExportDataAsync(products, "المنتجات", ExportFormat.CSV);
        }

        public static async Task<bool> ExportSalesToExcelAsync(IEnumerable<Sale> sales)
        {
            return await ExportDataAsync(sales, "المبيعات", ExportFormat.Excel);
        }

        public static async Task<bool> ExportCustomersToCsvAsync(IEnumerable<Customer> customers)
        {
            return await ExportDataAsync(customers, "العملاء", ExportFormat.CSV);
        }

        public static async Task<bool> ExportInventoryToPdfAsync(IEnumerable<Product> inventory)
        {
            return await ExportDataAsync(inventory, "تقرير_المخزون", ExportFormat.PDF);
        }
    }
}
