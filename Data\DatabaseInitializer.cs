using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Models;
using System.Security.Cryptography;
using System.Text;

namespace DateFactoryApp.Data
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeAsync(AppDbContext context)
        {
            try
            {
                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Check if data already exists
                if (await context.Users.AnyAsync())
                {
                    return; // Database has been seeded
                }

                // Seed initial data
                await SeedUsersAsync(context);
                await SeedCategoriesAsync(context);
                await SeedSampleProductsAsync(context);
                await SeedSampleCustomersAsync(context);
                await SeedSampleSuppliersAsync(context);

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log the error
                System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex.Message}");
                throw;
            }
        }

        private static async Task SeedUsersAsync(AppDbContext context)
        {
            var users = new[]
            {
                new User
                {
                    Username = "admin",
                    PasswordHash = HashPassword("admin123"),
                    FullName = "مدير النظام",
                    Role = UserRole.Manager,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new User
                {
                    Username = "supervisor",
                    PasswordHash = HashPassword("super123"),
                    FullName = "مشرف المصنع",
                    Role = UserRole.Supervisor,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new User
                {
                    Username = "cashier",
                    PasswordHash = HashPassword("cash123"),
                    FullName = "كاشير المبيعات",
                    Role = UserRole.Cashier,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Users.AddRangeAsync(users);
        }

        private static async Task SeedCategoriesAsync(AppDbContext context)
        {
            var categories = new[]
            {
                new ProductCategory
                {
                    Name = "تمور خام",
                    Code = "RAW",
                    Description = "التمور الخام غير المعالجة",
                    IsActive = true,
                    SortOrder = 1,
                    CreatedAt = DateTime.Now
                },
                new ProductCategory
                {
                    Name = "تمور معالجة",
                    Code = "PROC",
                    Description = "التمور المعالجة والمنظفة",
                    IsActive = true,
                    SortOrder = 2,
                    CreatedAt = DateTime.Now
                },
                new ProductCategory
                {
                    Name = "تمور معبأة",
                    Code = "PACK",
                    Description = "التمور المعبأة والجاهزة للبيع",
                    IsActive = true,
                    SortOrder = 3,
                    CreatedAt = DateTime.Now
                },
                new ProductCategory
                {
                    Name = "منتجات التمور",
                    Code = "PROD",
                    Description = "منتجات مشتقة من التمور",
                    IsActive = true,
                    SortOrder = 4,
                    CreatedAt = DateTime.Now
                }
            };

            await context.ProductCategories.AddRangeAsync(categories);
        }

        private static async Task SeedSampleProductsAsync(AppDbContext context)
        {
            var products = new[]
            {
                new Product
                {
                    Name = "تمر مجهول ممتاز",
                    Code = "RAW0001",
                    Description = "تمر مجهول درجة أولى",
                    CategoryId = 1,
                    Type = ProductType.RawDates,
                    Quality = DateQuality.Premium,
                    PackageSize = PackageSize.Kg25,
                    PurchasePrice = 45.00m,
                    SalePrice = 65.00m,
                    WholesalePrice = 55.00m,
                    MinStockLevel = 50,
                    MaxStockLevel = 500,
                    CurrentStock = 200,
                    Unit = "كيلو",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Name = "تمر صقعي معبأ",
                    Code = "PACK0001",
                    Description = "تمر صقعي معبأ 1 كيلو",
                    CategoryId = 3,
                    Type = ProductType.PackagedDates,
                    Quality = DateQuality.Good,
                    PackageSize = PackageSize.Kg1,
                    PurchasePrice = 25.00m,
                    SalePrice = 35.00m,
                    WholesalePrice = 30.00m,
                    MinStockLevel = 100,
                    MaxStockLevel = 1000,
                    CurrentStock = 350,
                    Unit = "علبة",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Name = "دبس التمر الطبيعي",
                    Code = "PROD0001",
                    Description = "دبس تمر طبيعي 100%",
                    CategoryId = 4,
                    Type = ProductType.DateSyrup,
                    Quality = DateQuality.Premium,
                    PackageSize = PackageSize.Gram500,
                    PurchasePrice = 15.00m,
                    SalePrice = 25.00m,
                    WholesalePrice = 20.00m,
                    MinStockLevel = 50,
                    MaxStockLevel = 300,
                    CurrentStock = 120,
                    Unit = "زجاجة",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Products.AddRangeAsync(products);
        }

        private static async Task SeedSampleCustomersAsync(AppDbContext context)
        {
            var customers = new[]
            {
                new Customer
                {
                    Name = "محل الأصيل للتمور",
                    Code = "CUST0001",
                    Phone = "0501234567",
                    Address = "شارع الملك فهد، الرياض",
                    City = "الرياض",
                    Region = "الرياض",
                    Type = CustomerType.Retail,
                    CreditLimit = 10000.00m,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Customer
                {
                    Name = "شركة التمور الذهبية",
                    Code = "CUST0002",
                    Phone = "0507654321",
                    Address = "المنطقة الصناعية، الدمام",
                    City = "الدمام",
                    Region = "الشرقية",
                    Type = CustomerType.Wholesale,
                    CreditLimit = 50000.00m,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Customer
                {
                    Name = "مؤسسة النخيل التجارية",
                    Code = "CUST0003",
                    Phone = "0551234567",
                    Address = "حي الملز، الرياض",
                    City = "الرياض",
                    Region = "الرياض",
                    Type = CustomerType.Distributor,
                    CreditLimit = 25000.00m,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Customers.AddRangeAsync(customers);
        }

        private static async Task SeedSampleSuppliersAsync(AppDbContext context)
        {
            var suppliers = new[]
            {
                new Supplier
                {
                    Name = "مزرعة الواحة للتمور",
                    Code = "SUPP0001",
                    Phone = "0501111111",
                    Address = "القصيم، بريدة",
                    City = "بريدة",
                    Region = "القصيم",
                    ContactPerson = "أحمد المزارع",
                    ContactPhone = "0501111111",
                    Type = SupplierType.DateFarmer,
                    PaymentTerms = 30,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Supplier
                {
                    Name = "مصنع التعبئة الحديث",
                    Code = "SUPP0002",
                    Phone = "0502222222",
                    Address = "المنطقة الصناعية، الرياض",
                    City = "الرياض",
                    Region = "الرياض",
                    ContactPerson = "محمد المدير",
                    ContactPhone = "0502222222",
                    Type = SupplierType.Packaging,
                    PaymentTerms = 15,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Suppliers.AddRangeAsync(suppliers);
        }

        private static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
