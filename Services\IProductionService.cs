using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface IProductionService
    {
        Task<IEnumerable<ProductionRecord>> GetAllProductionRecordsAsync();
        Task<IEnumerable<ProductionRecord>> GetProductionRecordsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<ProductionRecord>> GetProductionRecordsByProductAsync(int productId);
        Task<ProductionRecord?> GetProductionRecordByIdAsync(int id);
        Task<ProductionRecord?> GetProductionRecordByBatchNumberAsync(string batchNumber);
        Task<ProductionRecord> CreateProductionRecordAsync(ProductionRecord record);
        Task<ProductionRecord> UpdateProductionRecordAsync(ProductionRecord record);
        Task<bool> DeleteProductionRecordAsync(int id);
        Task<string> GenerateBatchNumberAsync();
        Task<bool> StartProductionAsync(int recordId);
        Task<bool> CompleteProductionAsync(int recordId);
        Task<bool> CancelProductionAsync(int recordId, string reason);
        Task<decimal> GetTotalProductionAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<IEnumerable<ProductionRecord>> GetActiveProductionRecordsAsync();
    }
}
