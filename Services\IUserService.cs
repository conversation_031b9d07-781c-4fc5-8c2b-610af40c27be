using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface IUserService
    {
        Task<User?> AuthenticateAsync(string username, string password);
        Task<User?> GetCurrentUserAsync();
        Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User?> GetUserByIdAsync(int id);
        Task<User> CreateUserAsync(User user, string password);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null);
        void SetCurrentUser(User user);
        bool HasPermission(UserRole requiredRole);
    }
}
