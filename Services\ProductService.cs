using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class ProductService : IProductService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;

        public ProductService(AppDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetActiveProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted && p.IsActive && p.CurrentStock <= p.MinStockLevel)
                .OrderBy(p => (double)p.CurrentStock)
                .ToListAsync();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            return await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
        }

        public async Task<Product?> GetProductByCodeAsync(string code)
        {
            return await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Code == code && !p.IsDeleted);
        }

        public async Task<Product> CreateProductAsync(Product product)
        {
            var currentUser = await _userService.GetCurrentUserAsync();
            product.CreatedBy = currentUser?.Username;
            product.CreatedAt = DateTime.Now;

            // Generate code if not provided
            if (string.IsNullOrEmpty(product.Code))
            {
                product.Code = await GenerateProductCodeAsync(product.CategoryId);
            }

            _context.Products.Add(product);
            await _context.SaveChangesAsync();
            return product;
        }

        public async Task<Product> UpdateProductAsync(Product product)
        {
            var existingProduct = await _context.Products.FindAsync(product.Id);
            if (existingProduct == null)
                throw new ArgumentException("المنتج غير موجود");

            var currentUser = await _userService.GetCurrentUserAsync();

            existingProduct.Name = product.Name;
            existingProduct.Description = product.Description;
            existingProduct.CategoryId = product.CategoryId;
            existingProduct.Type = product.Type;
            existingProduct.Quality = product.Quality;
            existingProduct.PackageSize = product.PackageSize;
            existingProduct.PurchasePrice = product.PurchasePrice;
            existingProduct.SalePrice = product.SalePrice;
            existingProduct.WholesalePrice = product.WholesalePrice;
            existingProduct.MinStockLevel = product.MinStockLevel;
            existingProduct.MaxStockLevel = product.MaxStockLevel;
            existingProduct.Unit = product.Unit;
            existingProduct.IsActive = product.IsActive;
            existingProduct.ImagePath = product.ImagePath;
            existingProduct.Barcode = product.Barcode;
            existingProduct.ExpiryDate = product.ExpiryDate;
            existingProduct.UpdatedAt = DateTime.Now;
            existingProduct.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return existingProduct;
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            product.IsDeleted = true;
            product.UpdatedAt = DateTime.Now;
            product.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> IsCodeAvailableAsync(string code, int? excludeProductId = null)
        {
            var query = _context.Products.Where(p => p.Code == code && !p.IsDeleted);

            if (excludeProductId.HasValue)
                query = query.Where(p => p.Id != excludeProductId.Value);

            return !await query.AnyAsync();
        }

        public async Task UpdateStockAsync(int productId, decimal quantity, TransactionType transactionType)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                throw new ArgumentException("المنتج غير موجود");

            var oldStock = product.CurrentStock;

            switch (transactionType)
            {
                case TransactionType.Purchase:
                case TransactionType.Production:
                case TransactionType.Return:
                    product.CurrentStock += (decimal)quantity;
                    break;
                case TransactionType.Sale:
                case TransactionType.Damage:
                case TransactionType.Expired:
                    product.CurrentStock -= (decimal)quantity;
                    break;
                case TransactionType.Adjustment:
                    product.CurrentStock = (decimal)quantity;
                    break;
            }

            product.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<ProductCategory>> GetCategoriesAsync()
        {
            return await _context.ProductCategories
                .Where(c => !c.IsDeleted)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<ProductCategory> CreateCategoryAsync(ProductCategory category)
        {
            var currentUser = await _userService.GetCurrentUserAsync();
            category.CreatedBy = currentUser?.Username;
            category.CreatedAt = DateTime.Now;

            _context.ProductCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<ProductCategory> UpdateCategoryAsync(ProductCategory category)
        {
            var existingCategory = await _context.ProductCategories.FindAsync(category.Id);
            if (existingCategory == null)
                throw new ArgumentException("التصنيف غير موجود");

            var currentUser = await _userService.GetCurrentUserAsync();

            existingCategory.Name = category.Name;
            existingCategory.Description = category.Description;
            existingCategory.Code = category.Code;
            existingCategory.IsActive = category.IsActive;
            existingCategory.ImagePath = category.ImagePath;
            existingCategory.SortOrder = category.SortOrder;
            existingCategory.UpdatedAt = DateTime.Now;
            existingCategory.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return existingCategory;
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var category = await _context.ProductCategories.FindAsync(id);
            if (category == null)
                return false;

            // Check if category has products
            var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id && !p.IsDeleted);
            if (hasProducts)
                throw new InvalidOperationException("لا يمكن حذف التصنيف لأنه يحتوي على منتجات");

            var currentUser = await _userService.GetCurrentUserAsync();

            category.IsDeleted = true;
            category.UpdatedAt = DateTime.Now;
            category.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        private async Task<string> GenerateProductCodeAsync(int categoryId)
        {
            var category = await _context.ProductCategories.FindAsync(categoryId);
            var prefix = category?.Code ?? "PRD";

            var lastProduct = await _context.Products
                .Where(p => p.Code!.StartsWith(prefix))
                .OrderByDescending(p => p.Code)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastProduct != null && lastProduct.Code != null)
            {
                var numberPart = lastProduct.Code.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }
    }
}
