<Window x:Class="DateFactoryApp.Views.AboutWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="حول البرنامج"
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True">

    <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth4"
                         materialDesign:ShadowAssist.ShadowEdges="All">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Background="{StaticResource PrimaryBrush}">
                <Grid.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Grid.Effect>

                <!-- Close Button -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                        HorizontalAlignment="Left" VerticalAlignment="Top"
                        Margin="10" Foreground="White"
                        Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                </Button>

                <!-- Header Content -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,20">
                    <materialDesign:PackIcon Kind="Factory" Width="80" Height="80"
                                           Foreground="White" Margin="0,0,0,16"/>
                    <TextBlock Text="نظام إدارة مصنع ومحل التمور"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="White" HorizontalAlignment="Center"
                             FontWeight="Bold"/>
                    <TextBlock Text="Date Factory Management System"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="White" HorizontalAlignment="Center"
                             Opacity="0.8" Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>

            <!-- Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="30">
                <StackPanel>

                    <!-- Version Info -->
                    <materialDesign:Card Style="{StaticResource InfoCard}" Margin="0,0,0,16">
                        <StackPanel Margin="20">
                            <TextBlock Text="معلومات الإصدار"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     FontWeight="Medium" Margin="0,0,0,12"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الإصدار:" FontWeight="Medium" Margin="0,0,8,4"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="1.0.0" Margin="0,0,0,4"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الإصدار:" FontWeight="Medium" Margin="0,0,8,4"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="2024/12/19" Margin="0,0,0,4"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع الإصدار:" FontWeight="Medium" Margin="0,0,8,4"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="إصدار مستقر" Margin="0,0,0,4"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="الترخيص:" FontWeight="Medium" Margin="0,0,8,4"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="ترخيص تجاري" Margin="0,0,0,4"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Features -->
                    <materialDesign:Card Style="{StaticResource InfoCard}" Margin="0,0,0,16">
                        <StackPanel Margin="20">
                            <TextBlock Text="الميزات الرئيسية"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     FontWeight="Medium" Margin="0,0,0,12"/>

                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="إدارة شاملة للمبيعات والفواتير"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="تتبع المخزون مع تنبيهات ذكية"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="إدارة العملاء والموردين"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="تقارير مالية وإحصائية متقدمة"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="نسخ احتياطي ومزامنة سحابية"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"
                                                           Foreground="{StaticResource SuccessBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="واجهة عصرية بتصميم Material Design"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Company Info -->
                    <materialDesign:Card Style="{StaticResource InfoCard}" Margin="0,0,0,16">
                        <StackPanel Margin="20">
                            <TextBlock Text="معلومات الشركة"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     FontWeight="Medium" Margin="0,0,0,12"/>

                            <StackPanel>
                                <TextBlock Text="شركة التمور المتقدمة" FontWeight="Medium" Margin="0,0,0,4"/>
                                <TextBlock Text="متخصصون في حلول إدارة مصانع ومحلات التمور"
                                         Opacity="0.7" Margin="0,0,0,8"/>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Email" Width="16" Height="16"
                                                           Foreground="{StaticResource PrimaryBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="<EMAIL>"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Phone" Width="16" Height="16"
                                                           Foreground="{StaticResource PrimaryBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="+966 50 123 4567"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Web" Width="16" Height="16"
                                                           Foreground="{StaticResource PrimaryBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="www.datefactory.com"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Technical Info -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel Margin="20">
                            <TextBlock Text="المعلومات التقنية"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     FontWeight="Medium" Margin="0,0,0,12"/>

                            <StackPanel>
                                <TextBlock Text="• تم تطوير البرنامج باستخدام .NET 8.0" Margin="0,2"/>
                                <TextBlock Text="• واجهة المستخدم: WPF مع Material Design" Margin="0,2"/>
                                <TextBlock Text="• قاعدة البيانات: SQLite مع Entity Framework" Margin="0,2"/>
                                <TextBlock Text="• نمط التصميم: MVVM" Margin="0,2"/>
                                <TextBlock Text="• دعم كامل للغة العربية" Margin="0,2"/>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Height="1"/>
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center"
                       Margin="20" VerticalAlignment="Center">
                <TextBlock Text="© 2024 جميع الحقوق محفوظة - شركة التمور المتقدمة"
                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                         Opacity="0.7"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</Window>
