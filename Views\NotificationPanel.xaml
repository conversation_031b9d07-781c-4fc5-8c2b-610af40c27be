<UserControl x:Class="DateFactoryApp.Views.NotificationPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:helpers="clr-namespace:DateFactoryApp.Helpers"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Notification Item Template -->
        <DataTemplate x:Key="NotificationTemplate" DataType="{x:Type helpers:NotificationItem}">
            <materialDesign:Card Margin="8,4" 
                               materialDesign:ShadowAssist.ShadowDepth="Depth2"
                               Opacity="{Binding IsVisible, Converter={StaticResource BooleanToOpacityConverter}}">
                <materialDesign:Card.Style>
                    <Style TargetType="materialDesign:Card">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Type}" Value="Success">
                                <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Type}" Value="Error">
                                <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Type}" Value="Warning">
                                <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Type}" Value="Info">
                                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </materialDesign:Card.Style>

                <Grid Margin="16,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Icon -->
                    <materialDesign:PackIcon Grid.Column="0" 
                                           Kind="{Binding Icon}"
                                           Width="24" Height="24"
                                           Foreground="White"
                                           VerticalAlignment="Top"
                                           Margin="0,0,12,0"/>

                    <!-- Content -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="{Binding Title}"
                                 Foreground="White"
                                 FontWeight="Medium"
                                 FontSize="14"
                                 Margin="0,0,0,4"/>
                        
                        <TextBlock Text="{Binding Message}"
                                 Foreground="White"
                                 FontSize="12"
                                 Opacity="0.9"
                                 TextWrapping="Wrap"/>
                        
                        <TextBlock Text="{Binding Timestamp, StringFormat='HH:mm:ss'}"
                                 Foreground="White"
                                 FontSize="10"
                                 Opacity="0.7"
                                 Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Close Button -->
                    <Button Grid.Column="2"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Width="24" Height="24"
                          Foreground="White"
                          VerticalAlignment="Top"
                          Click="CloseNotification_Click"
                          Tag="{Binding}">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </Grid>

                <!-- Slide in animation -->
                <materialDesign:Card.Triggers>
                    <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                                               From="300" To="0" Duration="0:0:0.3">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut"/>
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               From="0" To="1" Duration="0:0:0.3"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </materialDesign:Card.Triggers>

                <materialDesign:Card.RenderTransform>
                    <TranslateTransform/>
                </materialDesign:Card.RenderTransform>
            </materialDesign:Card>
        </DataTemplate>

        <!-- Boolean to Opacity Converter -->
        <helpers:BooleanToOpacityConverter x:Key="BooleanToOpacityConverter"/>
    </UserControl.Resources>

    <Grid>
        <!-- Notification List -->
        <ItemsControl ItemsSource="{Binding Source={x:Static helpers:NotificationService.Instance}, Path=Notifications}"
                      ItemTemplate="{StaticResource NotificationTemplate}"
                      VerticalAlignment="Top"
                      HorizontalAlignment="Left"
                      MaxWidth="400">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Vertical"/>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
        </ItemsControl>
    </Grid>
</UserControl>
