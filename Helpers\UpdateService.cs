using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;

namespace DateFactoryApp.Helpers
{
    public class UpdateInfo
    {
        public string Version { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string ReleaseNotes { get; set; } = string.Empty;
        public DateTime ReleaseDate { get; set; }
        public bool IsRequired { get; set; }
        public long FileSize { get; set; }
        public string Checksum { get; set; } = string.Empty;
    }

    public class UpdateService
    {
        private static UpdateService? _instance;
        private readonly HttpClient _httpClient;
        private readonly string _currentVersion = "1.0.0";
        private readonly string _updateCheckUrl = "https://api.datefactory.com/updates/check";

        public static UpdateService Instance => _instance ??= new UpdateService();

        public event EventHandler<UpdateInfo>? UpdateAvailable;
        public event EventHandler<int>? DownloadProgress;

        private UpdateService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "DateFactoryApp/1.0");
        }

        public async Task<UpdateInfo?> CheckForUpdatesAsync()
        {
            try
            {
                var requestData = new
                {
                    currentVersion = _currentVersion,
                    platform = "windows",
                    architecture = Environment.Is64BitProcess ? "x64" : "x86"
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_updateCheckUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var updateInfo = JsonSerializer.Deserialize<UpdateInfo>(responseJson);

                    if (updateInfo != null && IsNewerVersion(updateInfo.Version, _currentVersion))
                    {
                        UpdateAvailable?.Invoke(this, updateInfo);
                        return updateInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user unless explicitly checking
                System.Diagnostics.Debug.WriteLine($"Update check failed: {ex.Message}");
            }

            return null;
        }

        public async Task<bool> DownloadAndInstallUpdateAsync(UpdateInfo updateInfo)
        {
            try
            {
                NotificationService.Info("بدء تحميل التحديث", "جاري تحميل الإصدار الجديد...");

                var tempPath = Path.GetTempPath();
                var updateFileName = $"DateFactoryApp_Update_{updateInfo.Version}.exe";
                var updateFilePath = Path.Combine(tempPath, updateFileName);

                // Download update file
                using (var response = await _httpClient.GetAsync(updateInfo.DownloadUrl, HttpCompletionOption.ResponseHeadersRead))
                {
                    response.EnsureSuccessStatusCode();

                    var totalBytes = response.Content.Headers.ContentLength ?? 0;
                    var downloadedBytes = 0L;

                    using (var contentStream = await response.Content.ReadAsStreamAsync())
                    using (var fileStream = new FileStream(updateFilePath, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        var buffer = new byte[8192];
                        int bytesRead;

                        while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            await fileStream.WriteAsync(buffer, 0, bytesRead);
                            downloadedBytes += bytesRead;

                            if (totalBytes > 0)
                            {
                                var progress = (int)((downloadedBytes * 100) / totalBytes);
                                DownloadProgress?.Invoke(this, progress);
                            }
                        }
                    }
                }

                // Verify checksum if provided
                if (!string.IsNullOrEmpty(updateInfo.Checksum))
                {
                    var fileChecksum = await CalculateFileChecksumAsync(updateFilePath);
                    if (fileChecksum != updateInfo.Checksum)
                    {
                        File.Delete(updateFilePath);
                        NotificationService.Error("فشل التحقق من التحديث", "ملف التحديث تالف");
                        return false;
                    }
                }

                NotificationService.Success("تم تحميل التحديث", "سيتم تطبيق التحديث عند إعادة تشغيل التطبيق");

                // Create update script
                await CreateUpdateScriptAsync(updateFilePath);

                return true;
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في تحميل التحديث", ex.Message);
                return false;
            }
        }

        public async Task<bool> ShowUpdateDialogAsync(UpdateInfo updateInfo)
        {
            var result = MessageBox.Show(
                $"يتوفر إصدار جديد من التطبيق!\n\n" +
                $"الإصدار الحالي: {_currentVersion}\n" +
                $"الإصدار الجديد: {updateInfo.Version}\n" +
                $"تاريخ الإصدار: {updateInfo.ReleaseDate:yyyy/MM/dd}\n\n" +
                $"ملاحظات الإصدار:\n{updateInfo.ReleaseNotes}\n\n" +
                $"هل تريد تحميل وتثبيت التحديث الآن؟",
                "تحديث متوفر",
                MessageBoxButton.YesNo,
                MessageBoxImage.Information);

            if (result == MessageBoxResult.Yes)
            {
                return await DownloadAndInstallUpdateAsync(updateInfo);
            }

            return false;
        }

        public void CheckForUpdatesInBackground()
        {
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(5000); // Wait 5 seconds after startup
                    var updateInfo = await CheckForUpdatesAsync();
                    
                    if (updateInfo != null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            if (updateInfo.IsRequired)
                            {
                                MessageBox.Show(
                                    "يتوفر تحديث مطلوب للتطبيق. سيتم إغلاق التطبيق لتطبيق التحديث.",
                                    "تحديث مطلوب",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Warning);
                                
                                DownloadAndInstallUpdateAsync(updateInfo);
                            }
                            else
                            {
                                NotificationService.Info("تحديث متوفر", 
                                    $"الإصدار {updateInfo.Version} متوفر للتحميل");
                            }
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Background update check failed: {ex.Message}");
                }
            });
        }

        private bool IsNewerVersion(string newVersion, string currentVersion)
        {
            try
            {
                var newVer = new Version(newVersion);
                var currentVer = new Version(currentVersion);
                return newVer > currentVer;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> CalculateFileChecksumAsync(string filePath)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            using (var stream = File.OpenRead(filePath))
            {
                var hash = await Task.Run(() => sha256.ComputeHash(stream));
                return Convert.ToBase64String(hash);
            }
        }

        private async Task CreateUpdateScriptAsync(string updateFilePath)
        {
            var currentExePath = Process.GetCurrentProcess().MainModule?.FileName;
            var scriptPath = Path.Combine(Path.GetTempPath(), "update_datefactory.bat");

            var script = $@"@echo off
echo جاري تطبيق التحديث...
timeout /t 3 /nobreak > nul
taskkill /f /im DateFactoryApp.exe > nul 2>&1
timeout /t 2 /nobreak > nul
copy ""{updateFilePath}"" ""{currentExePath}"" /y
if %errorlevel% equ 0 (
    echo تم تطبيق التحديث بنجاح
    start """" ""{currentExePath}""
) else (
    echo فشل في تطبيق التحديث
    pause
)
del ""{updateFilePath}""
del ""%~f0""
";

            await File.WriteAllTextAsync(scriptPath, script, System.Text.Encoding.UTF8);

            // Set script to run on next startup
            var startupFolder = Environment.GetFolderPath(Environment.SpecialFolder.Startup);
            var startupScriptPath = Path.Combine(startupFolder, "DateFactoryUpdate.bat");
            
            if (File.Exists(startupScriptPath))
                File.Delete(startupScriptPath);
                
            File.Copy(scriptPath, startupScriptPath);
        }

        public string GetCurrentVersion()
        {
            return _currentVersion;
        }

        public async Task<string> GetLatestVersionAsync()
        {
            try
            {
                var updateInfo = await CheckForUpdatesAsync();
                return updateInfo?.Version ?? _currentVersion;
            }
            catch
            {
                return _currentVersion;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
