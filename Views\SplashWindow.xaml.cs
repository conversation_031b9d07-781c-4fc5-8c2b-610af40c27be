using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace DateFactoryApp.Views
{
    public partial class SplashWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly string[] _loadingMessages = {
            "جاري تحميل قاعدة البيانات...",
            "جاري تحميل الخدمات...",
            "جاري تحميل واجهة المستخدم...",
            "جاري التحقق من التحديثات...",
            "جاري إعداد النظام...",
            "تم التحميل بنجاح!"
        };
        private int _currentMessageIndex = 0;

        public SplashWindow()
        {
            InitializeComponent();
            
            // Initialize timer for loading messages
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(800)
            };
            _timer.Tick += Timer_Tick;
            
            // Start the loading process
            Loaded += SplashWindow_Loaded;
        }

        private async void SplashWindow_Loaded(object sender, RoutedEventArgs e)
        {
            _timer.Start();
            
            // Simulate loading process
            await SimulateLoadingAsync();
            
            // Show login window
            ShowLoginWindow();
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            if (_currentMessageIndex < _loadingMessages.Length)
            {
                LoadingText.Text = _loadingMessages[_currentMessageIndex];
                _currentMessageIndex++;
            }
            else
            {
                _timer.Stop();
            }
        }

        private async Task SimulateLoadingAsync()
        {
            // Simulate various loading operations
            await Task.Delay(1000); // Database initialization
            await Task.Delay(800);  // Services loading
            await Task.Delay(600);  // UI loading
            await Task.Delay(500);  // Update check
            await Task.Delay(400);  // System setup
            await Task.Delay(300);  // Final setup
        }

        private void ShowLoginWindow()
        {
            try
            {
                // Create and show login window
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                
                // Close splash window
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
