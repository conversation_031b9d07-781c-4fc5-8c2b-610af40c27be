# نظام إدارة مصنع ومحل التمور
# Date Factory Management System

Write-Host "========================================" -ForegroundColor Green
Write-Host "    نظام إدارة مصنع ومحل التمور" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "جاري التحقق من متطلبات التشغيل..." -ForegroundColor Cyan

# Check if .NET 8 is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "تم العثور على .NET Runtime: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "خطأ: .NET 8.0 غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تحميل وتثبيت .NET 8.0 Runtime من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Blue
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# Create necessary directories
$directories = @("logs", "Backups", "Reports", "Resources")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "تم إنشاء مجلد: $dir" -ForegroundColor Green
    }
}

Write-Host "جاري استعادة الحزم المطلوبة..." -ForegroundColor Cyan
try {
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في استعادة الحزم"
    }
    Write-Host "تم استعادة الحزم بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في استعادة الحزم: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "جاري بناء المشروع..." -ForegroundColor Cyan
try {
    dotnet build --configuration Release
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في بناء المشروع"
    }
    Write-Host "تم بناء المشروع بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في بناء المشروع: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "       تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "بيانات الدخول الافتراضية:" -ForegroundColor Cyan
Write-Host "اسم المستخدم: admin" -ForegroundColor White
Write-Host "كلمة المرور: admin123" -ForegroundColor White
Write-Host ""

# Run the application
try {
    Write-Host "جاري تشغيل التطبيق..." -ForegroundColor Green
    Start-Process -FilePath ".\bin\Release\net8.0-windows\DateFactoryApp.exe" -Wait
    Write-Host "تم إغلاق التطبيق بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تشغيل التطبيق: $_" -ForegroundColor Red
    Write-Host "جاري المحاولة باستخدام dotnet run..." -ForegroundColor Yellow
    try {
        dotnet run --configuration Release
    } catch {
        Write-Host "فشل في تشغيل التطبيق: $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "تم إنهاء التطبيق" -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج"
