@echo off
chcp 65001 > nul
title اختبار تطبيق إدارة مصنع التمور

echo.
echo ================================================
echo    🧪 اختبار تطبيق إدارة مصنع التمور
echo    📋 فحص سريع للتطبيق
echo ================================================
echo.

echo 🔍 جاري فحص الملفات المطلوبة...

REM Check if executable exists
if not exist "bin\Release\net8.0-windows\DateFactoryApp.exe" (
    echo ❌ الملف التنفيذي غير موجود
    echo 🔨 جاري بناء التطبيق...
    dotnet build DateFactoryApp.csproj --configuration Release
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق
        pause
        exit /b 1
    )
)

echo ✅ الملف التنفيذي موجود

REM Check database
if exist "DateFactory.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️  قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً
)

REM Check logs directory
if not exist "logs" (
    echo 📁 إنشاء مجلد السجلات...
    mkdir logs
)

echo.
echo 🚀 جاري تشغيل التطبيق...
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.

REM Start the application
start "" "bin\Release\net8.0-windows\DateFactoryApp.exe"

echo ✅ تم تشغيل التطبيق بنجاح!
echo.
echo 📝 لمراجعة السجلات، تحقق من مجلد logs
echo 🔧 لإعادة بناء التطبيق، استخدم run.ps1
echo.
pause
