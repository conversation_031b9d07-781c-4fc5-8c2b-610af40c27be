using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<IEnumerable<Supplier>> GetActiveSuppliersAsync();
        Task<Supplier?> GetSupplierByIdAsync(int id);
        Task<Supplier?> GetSupplierByCodeAsync(string code);
        Task<Supplier> CreateSupplierAsync(Supplier supplier);
        Task<Supplier> UpdateSupplierAsync(Supplier supplier);
        Task<bool> DeleteSupplierAsync(int id);
        Task<bool> IsCodeAvailableAsync(string code, int? excludeSupplierId = null);
        Task<IEnumerable<Supplier>> GetSuppliersWithBalanceAsync();
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<bool> UpdateSupplierBalanceAsync(int supplierId, decimal amount);
        Task<IEnumerable<PurchaseOrder>> GetSupplierPurchaseOrdersAsync(int supplierId);
        Task<string> GenerateSupplierCodeAsync();
    }
}
