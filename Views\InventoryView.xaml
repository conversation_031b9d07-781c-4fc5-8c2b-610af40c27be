<UserControl x:Class="DateFactoryApp.Views.InventoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة المخزون" Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Date Range -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <DatePicker materialDesign:HintAssist.Hint="من تاريخ"
                              SelectedDate="{Binding StartDate}" Margin="0,0,8,0"/>
                    <DatePicker materialDesign:HintAssist.Hint="إلى تاريخ"
                              SelectedDate="{Binding EndDate}" Margin="0,0,8,0"/>
                    <Button Content="تحديث" Command="{Binding LoadDataCommand}" Margin="8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="تعديل مخزون" Command="{Binding StartAdjustmentCommand}"/>
                    <Button Content="منتجات منخفضة" Command="{Binding ShowLowStockCommand}" Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Transactions List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="حركات المخزون" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Transactions}" 
                            SelectedItem="{Binding SelectedTransaction}"
                            Style="{StaticResource CustomDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الحركة" Binding="{Binding TransactionNumber}" Width="120"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding TransactionDate, StringFormat='yyyy/MM/dd'}" Width="100"/>
                            <DataGridTextColumn Header="المنتج" Binding="{Binding Product.Name}" Width="150"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="80"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="المرجع" Binding="{Binding Reference}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Side Panel -->
            <StackPanel Grid.Column="1">
                <!-- Low Stock Products -->
                <materialDesign:Card Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="منتجات منخفضة المخزون" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>
                        <ListBox ItemsSource="{Binding LowStockProducts}" MaxHeight="200" Margin="16,0,16,16">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Margin="0,4">
                                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding CurrentStock, StringFormat='المخزون: {0}'}" 
                                                 Foreground="{StaticResource ErrorBrush}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Quick Actions -->
                <materialDesign:Card>
                    <StackPanel Margin="16">
                        <TextBlock Text="إجراءات سريعة" Style="{StaticResource SectionTitle}" Margin="0,0,0,8"/>
                        <Button Content="عرض المنتجات المنتهية" Command="{Binding ShowOutOfStockCommand}" 
                              Style="{StaticResource WarningButton}" Margin="0,4"/>
                        <Button Content="تقرير المخزون" Command="{Binding GenerateInventoryReportCommand}" 
                              Style="{StaticResource SecondaryButton}" Margin="0,4"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
