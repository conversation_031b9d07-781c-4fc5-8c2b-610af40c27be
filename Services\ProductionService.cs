using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class ProductionService : IProductionService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;
        private readonly IInventoryService _inventoryService;

        public ProductionService(AppDbContext context, IUserService userService, IInventoryService inventoryService)
        {
            _context = context;
            _userService = userService;
            _inventoryService = inventoryService;
        }

        public async Task<IEnumerable<ProductionRecord>> GetAllProductionRecordsAsync()
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .Where(pr => !pr.IsDeleted)
                .OrderByDescending(pr => pr.ProductionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductionRecord>> GetProductionRecordsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .Where(pr => !pr.IsDeleted && pr.ProductionDate >= startDate && pr.ProductionDate <= endDate)
                .OrderByDescending(pr => pr.ProductionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductionRecord>> GetProductionRecordsByProductAsync(int productId)
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .Where(pr => !pr.IsDeleted && pr.ProductId == productId)
                .OrderByDescending(pr => pr.ProductionDate)
                .ToListAsync();
        }

        public async Task<ProductionRecord?> GetProductionRecordByIdAsync(int id)
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .FirstOrDefaultAsync(pr => pr.Id == id && !pr.IsDeleted);
        }

        public async Task<ProductionRecord?> GetProductionRecordByBatchNumberAsync(string batchNumber)
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .FirstOrDefaultAsync(pr => pr.BatchNumber == batchNumber && !pr.IsDeleted);
        }

        public async Task<ProductionRecord> CreateProductionRecordAsync(ProductionRecord record)
        {
            var currentUser = await _userService.GetCurrentUserAsync();

            record.CreatedBy = currentUser?.Username;
            record.CreatedAt = DateTime.Now;
            record.SupervisorId = currentUser?.Id;

            if (string.IsNullOrEmpty(record.BatchNumber))
            {
                record.BatchNumber = await GenerateBatchNumberAsync();
            }

            _context.ProductionRecords.Add(record);
            await _context.SaveChangesAsync();
            return record;
        }

        public async Task<ProductionRecord> UpdateProductionRecordAsync(ProductionRecord record)
        {
            var existingRecord = await _context.ProductionRecords.FindAsync(record.Id);
            if (existingRecord == null)
                throw new ArgumentException("سجل الإنتاج غير موجود");

            var currentUser = await _userService.GetCurrentUserAsync();

            existingRecord.ProductionDate = record.ProductionDate;
            existingRecord.ProductId = record.ProductId;
            existingRecord.ProducedQuantity = record.ProducedQuantity;
            existingRecord.ProductionCost = record.ProductionCost;
            existingRecord.Status = record.Status;
            existingRecord.Notes = record.Notes;
            existingRecord.StartTime = record.StartTime;
            existingRecord.EndTime = record.EndTime;
            existingRecord.UpdatedAt = DateTime.Now;
            existingRecord.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return existingRecord;
        }

        public async Task<bool> DeleteProductionRecordAsync(int id)
        {
            var record = await _context.ProductionRecords.FindAsync(id);
            if (record == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            record.IsDeleted = true;
            record.UpdatedAt = DateTime.Now;
            record.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateBatchNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"BATCH-{today:yyyyMMdd}-";

            var lastRecord = await _context.ProductionRecords
                .Where(pr => pr.BatchNumber.StartsWith(prefix))
                .OrderByDescending(pr => pr.BatchNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastRecord != null)
            {
                var numberPart = lastRecord.BatchNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        public async Task<bool> StartProductionAsync(int recordId)
        {
            var record = await _context.ProductionRecords.FindAsync(recordId);
            if (record == null || record.Status != ProductionStatus.Planned)
                return false;

            record.Status = ProductionStatus.InProgress;
            record.StartTime = DateTime.Now;
            record.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CompleteProductionAsync(int recordId)
        {
            var record = await _context.ProductionRecords.FindAsync(recordId);
            if (record == null || record.Status != ProductionStatus.InProgress)
                return false;

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                record.Status = ProductionStatus.Completed;
                record.EndTime = DateTime.Now;
                record.UpdatedAt = DateTime.Now;

                // Create inventory transaction for produced items
                await _inventoryService.CreateTransactionAsync(new InventoryTransaction
                {
                    TransactionNumber = $"PROD-{record.BatchNumber}",
                    TransactionDate = DateTime.Now,
                    ProductId = record.ProductId,
                    Type = TransactionType.Production,
                    Quantity = record.ProducedQuantity,
                    UnitCost = record.ProductionCost / record.ProducedQuantity,
                    TotalCost = record.ProductionCost,
                    ProductionRecordId = record.Id,
                    UserId = record.SupervisorId ?? 1,
                    Reference = record.BatchNumber,
                    Notes = $"إنتاج دفعة {record.BatchNumber}"
                });

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> CancelProductionAsync(int recordId, string reason)
        {
            var record = await _context.ProductionRecords.FindAsync(recordId);
            if (record == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            record.Status = ProductionStatus.Cancelled;
            record.Notes = $"{record.Notes}\nملغي: {reason}";
            record.UpdatedAt = DateTime.Now;
            record.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal> GetTotalProductionAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.ProductionRecords
                .Where(pr => !pr.IsDeleted && pr.Status == ProductionStatus.Completed);

            if (startDate.HasValue)
                query = query.Where(pr => pr.ProductionDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(pr => pr.ProductionDate <= endDate.Value);

            return await query.SumAsync(pr => pr.ProducedQuantity);
        }

        public async Task<IEnumerable<ProductionRecord>> GetActiveProductionRecordsAsync()
        {
            return await _context.ProductionRecords
                .Include(pr => pr.Product)
                .Include(pr => pr.Supervisor)
                .Where(pr => !pr.IsDeleted &&
                           (pr.Status == ProductionStatus.Planned || pr.Status == ProductionStatus.InProgress))
                .OrderBy(pr => pr.ProductionDate)
                .ToListAsync();
        }
    }
}
