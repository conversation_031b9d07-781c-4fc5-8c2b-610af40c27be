using System;
using MaterialDesignThemes.Wpf;

namespace DateFactoryApp.Services
{
    public interface IThemeService
    {
        bool IsDarkTheme { get; }
        void SetTheme(bool isDark);
        void ToggleTheme();
        void SetPrimaryColor(string color);
        void SetSecondaryColor(string color);
        string GetCurrentPrimaryColor();
        string GetCurrentSecondaryColor();
        void SaveThemeSettings();
        void LoadThemeSettings();
        event System.EventHandler<ThemeChangedEventArgs>? ThemeChanged;
    }

    public class ThemeChangedEventArgs : System.EventArgs
    {
        public bool IsDarkTheme { get; set; }
        public string PrimaryColor { get; set; }
        public string SecondaryColor { get; set; }
    }
}
