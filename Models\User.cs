using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DateFactoryApp.Models
{
    public class User : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string FullName { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [Required]
        public UserRole Role { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime? LastLoginAt { get; set; }

        // Navigation properties
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    }

    public enum UserRole
    {
        Manager = 1,    // مدير - صلاحيات كاملة
        Supervisor = 2, // مشرف - صلاحيات متوسطة
        Cashier = 3,    // كاشير - صلاحيات محدودة
        Worker = 4      // عامل - صلاحيات أساسية
    }
}
