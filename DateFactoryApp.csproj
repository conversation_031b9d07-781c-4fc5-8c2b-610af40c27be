<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <!-- <ApplicationIcon>Resources\app-icon.ico</ApplicationIcon> -->
    <AssemblyTitle>نظام إدارة مصنع ومحل التمور</AssemblyTitle>
    <AssemblyDescription>نظام شامل لإدارة جميع عمليات مصنع ومحل التمور</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="Models\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Reports\" />
  </ItemGroup>

</Project>
