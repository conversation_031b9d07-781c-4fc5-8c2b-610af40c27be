using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;

        public CustomerService(AppDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            return await _context.Customers
                .Where(c => !c.IsDeleted)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            return await _context.Customers
                .Where(c => !c.IsDeleted && c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
        }

        public async Task<Customer?> GetCustomerByCodeAsync(string code)
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.Code == code && !c.IsDeleted);
        }

        public async Task<Customer> CreateCustomerAsync(Customer customer)
        {
            var currentUser = await _userService.GetCurrentUserAsync();

            customer.CreatedBy = currentUser?.Username;
            customer.CreatedAt = DateTime.Now;

            if (string.IsNullOrEmpty(customer.Code))
            {
                customer.Code = await GenerateCustomerCodeAsync();
            }

            _context.Customers.Add(customer);
            await _context.SaveChangesAsync();
            return customer;
        }

        public async Task<Customer> UpdateCustomerAsync(Customer customer)
        {
            var existingCustomer = await _context.Customers.FindAsync(customer.Id);
            if (existingCustomer == null)
                throw new ArgumentException("العميل غير موجود");

            var currentUser = await _userService.GetCurrentUserAsync();

            existingCustomer.Name = customer.Name;
            existingCustomer.Phone = customer.Phone;
            existingCustomer.Email = customer.Email;
            existingCustomer.Address = customer.Address;
            existingCustomer.City = customer.City;
            existingCustomer.Region = customer.Region;
            existingCustomer.Type = customer.Type;
            existingCustomer.CreditLimit = customer.CreditLimit;
            existingCustomer.IsActive = customer.IsActive;
            existingCustomer.Notes = customer.Notes;
            existingCustomer.UpdatedAt = DateTime.Now;
            existingCustomer.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return existingCustomer;
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer == null)
                return false;

            // Check if customer has sales
            var hasSales = await _context.Sales.AnyAsync(s => s.CustomerId == id && !s.IsDeleted);
            if (hasSales)
                throw new InvalidOperationException("لا يمكن حذف العميل لأنه يحتوي على مبيعات");

            var currentUser = await _userService.GetCurrentUserAsync();

            customer.IsDeleted = true;
            customer.UpdatedAt = DateTime.Now;
            customer.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> IsCodeAvailableAsync(string code, int? excludeCustomerId = null)
        {
            var query = _context.Customers.Where(c => c.Code == code && !c.IsDeleted);

            if (excludeCustomerId.HasValue)
                query = query.Where(c => c.Id != excludeCustomerId.Value);

            return !await query.AnyAsync();
        }

        public async Task<IEnumerable<Customer>> GetCustomersWithBalanceAsync()
        {
            return await _context.Customers
                .Where(c => !c.IsDeleted && c.CurrentBalance != 0)
                .OrderByDescending(c => (double)c.CurrentBalance)
                .ToListAsync();
        }

        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            return customer?.CurrentBalance ?? 0;
        }

        public async Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            if (customer == null)
                return false;

            customer.CurrentBalance += amount;
            customer.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Sale>> GetCustomerSalesAsync(int customerId)
        {
            return await _context.Sales
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => s.CustomerId == customerId && !s.IsDeleted)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<string> GenerateCustomerCodeAsync()
        {
            var prefix = "CUST";

            var lastCustomer = await _context.Customers
                .Where(c => c.Code!.StartsWith(prefix))
                .OrderByDescending(c => c.Code)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastCustomer != null && lastCustomer.Code != null)
            {
                var numberPart = lastCustomer.Code.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }
    }
}
