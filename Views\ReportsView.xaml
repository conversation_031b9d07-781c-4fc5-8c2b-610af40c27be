<UserControl x:Class="DateFactoryApp.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="التقارير والإحصائيات" Style="{StaticResource PageTitle}"/>

        <!-- Report Selection -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Report Type and Date Range -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <ComboBox Width="200" materialDesign:HintAssist.Hint="نوع التقرير"
                            SelectedItem="{Binding SelectedReportType}" Margin="0,0,8,0"/>
                    <DatePicker materialDesign:HintAssist.Hint="من تاريخ"
                              SelectedDate="{Binding StartDate}" Margin="0,0,8,0"/>
                    <DatePicker materialDesign:HintAssist.Hint="إلى تاريخ"
                              SelectedDate="{Binding EndDate}" Margin="0,0,8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="إنشاء التقرير" Command="{Binding GenerateReportCommand}"/>
                    <Button Content="تصدير PDF" Command="{Binding ExportToPdfCommand}" Margin="8,0,0,0"/>
                    <Button Content="تصدير Excel" Command="{Binding ExportToExcelCommand}" Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Report Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Main Report Area -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="تقرير المبيعات" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>

                        <!-- Summary Cards -->
                        <UniformGrid Columns="3" Margin="0,0,0,16">
                            <materialDesign:Card Margin="0,0,4,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إجمالي المبيعات" Style="{StaticResource CardTitle}"/>
                                    <TextBlock Text="{Binding TotalSales, StringFormat='{}{0:C}'}" 
                                             Style="{StaticResource CardValue}"/>
                                </StackPanel>
                            </materialDesign:Card>
                            <materialDesign:Card Margin="4,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="عدد الفواتير" Style="{StaticResource CardTitle}"/>
                                    <TextBlock Text="{Binding TotalInvoices}" Style="{StaticResource CardValue}"/>
                                </StackPanel>
                            </materialDesign:Card>
                            <materialDesign:Card Margin="4,0,0,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="متوسط الفاتورة" Style="{StaticResource CardTitle}"/>
                                    <TextBlock Text="{Binding AverageInvoice, StringFormat='{}{0:C}'}" 
                                             Style="{StaticResource CardValue}"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <!-- Data Table -->
                        <DataGrid ItemsSource="{Binding ReportData}" 
                                Style="{StaticResource CustomDataGrid}" MaxHeight="400">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat='yyyy/MM/dd'}" Width="100"/>
                                <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                                <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat='{}{0:C}'}" Width="100"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Side Panel -->
            <StackPanel Grid.Column="1">
                <!-- Quick Reports -->
                <materialDesign:Card Margin="0,0,0,8">
                    <StackPanel Margin="16">
                        <TextBlock Text="تقارير سريعة" Style="{StaticResource SectionTitle}" Margin="0,0,0,8"/>
                        <Button Content="تقرير المبيعات اليومي" Command="{Binding DailySalesReportCommand}" 
                              Style="{StaticResource SecondaryButton}" Margin="0,4"/>
                        <Button Content="تقرير المخزون" Command="{Binding InventoryReportCommand}" 
                              Style="{StaticResource SecondaryButton}" Margin="0,4"/>
                        <Button Content="تقرير العملاء" Command="{Binding CustomersReportCommand}" 
                              Style="{StaticResource SecondaryButton}" Margin="0,4"/>
                        <Button Content="تقرير الموردين" Command="{Binding SuppliersReportCommand}" 
                              Style="{StaticResource SecondaryButton}" Margin="0,4"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Report Statistics -->
                <materialDesign:Card>
                    <StackPanel Margin="16">
                        <TextBlock Text="إحصائيات سريعة" Style="{StaticResource SectionTitle}" Margin="0,0,0,8"/>
                        <StackPanel Margin="0,4">
                            <TextBlock Text="مبيعات اليوم" FontWeight="Medium"/>
                            <TextBlock Text="{Binding TodaySales, StringFormat='{}{0:C}'}" 
                                     Foreground="{StaticResource PrimaryBrush}"/>
                        </StackPanel>
                        <StackPanel Margin="0,4">
                            <TextBlock Text="مبيعات الشهر" FontWeight="Medium"/>
                            <TextBlock Text="{Binding MonthSales, StringFormat='{}{0:C}'}" 
                                     Foreground="{StaticResource PrimaryBrush}"/>
                        </StackPanel>
                        <StackPanel Margin="0,4">
                            <TextBlock Text="عدد العملاء" FontWeight="Medium"/>
                            <TextBlock Text="{Binding TotalCustomers}"/>
                        </StackPanel>
                        <StackPanel Margin="0,4">
                            <TextBlock Text="عدد المنتجات" FontWeight="Medium"/>
                            <TextBlock Text="{Binding TotalProducts}"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
