using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;

namespace DateFactoryApp.Helpers
{
    public enum NotificationType
    {
        Success,
        Error,
        Warning,
        Info
    }

    public partial class NotificationItem : ObservableObject
    {
        [ObservableProperty]
        private string title = string.Empty;

        [ObservableProperty]
        private string message = string.Empty;

        [ObservableProperty]
        private NotificationType type;

        [ObservableProperty]
        private DateTime timestamp = DateTime.Now;

        [ObservableProperty]
        private bool isVisible = true;

        public string Icon => Type switch
        {
            NotificationType.Success => "CheckCircle",
            NotificationType.Error => "AlertCircle",
            NotificationType.Warning => "Alert",
            NotificationType.Info => "Information",
            _ => "Information"
        };

        public string TypeText => Type switch
        {
            NotificationType.Success => "نجح",
            NotificationType.Error => "خطأ",
            NotificationType.Warning => "تحذير",
            NotificationType.Info => "معلومات",
            _ => "معلومات"
        };
    }

    public class NotificationService
    {
        private static NotificationService? _instance;
        private readonly ObservableCollection<NotificationItem> _notifications;
        private readonly DispatcherTimer _cleanupTimer;

        public static NotificationService Instance => _instance ??= new NotificationService();

        public ObservableCollection<NotificationItem> Notifications => _notifications;

        private NotificationService()
        {
            _notifications = new ObservableCollection<NotificationItem>();
            
            // Setup cleanup timer to remove old notifications
            _cleanupTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(30)
            };
            _cleanupTimer.Tick += CleanupTimer_Tick;
            _cleanupTimer.Start();
        }

        public void ShowSuccess(string title, string message)
        {
            ShowNotification(title, message, NotificationType.Success);
        }

        public void ShowError(string title, string message)
        {
            ShowNotification(title, message, NotificationType.Error);
        }

        public void ShowWarning(string title, string message)
        {
            ShowNotification(title, message, NotificationType.Warning);
        }

        public void ShowInfo(string title, string message)
        {
            ShowNotification(title, message, NotificationType.Info);
        }

        public void ShowNotification(string title, string message, NotificationType type)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var notification = new NotificationItem
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.Now
                };

                _notifications.Insert(0, notification);

                // Auto-hide after delay based on type
                var hideDelay = type switch
                {
                    NotificationType.Success => TimeSpan.FromSeconds(3),
                    NotificationType.Info => TimeSpan.FromSeconds(4),
                    NotificationType.Warning => TimeSpan.FromSeconds(5),
                    NotificationType.Error => TimeSpan.FromSeconds(7),
                    _ => TimeSpan.FromSeconds(4)
                };

                var hideTimer = new DispatcherTimer { Interval = hideDelay };
                hideTimer.Tick += (s, e) =>
                {
                    hideTimer.Stop();
                    HideNotification(notification);
                };
                hideTimer.Start();

                // Limit number of visible notifications
                while (_notifications.Count > 5)
                {
                    _notifications.RemoveAt(_notifications.Count - 1);
                }
            });
        }

        public void HideNotification(NotificationItem notification)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                notification.IsVisible = false;
                
                // Remove after fade animation
                var removeTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(300) };
                removeTimer.Tick += (s, e) =>
                {
                    removeTimer.Stop();
                    _notifications.Remove(notification);
                };
                removeTimer.Start();
            });
        }

        public void ClearAll()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                _notifications.Clear();
            });
        }

        private void CleanupTimer_Tick(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var cutoffTime = DateTime.Now.AddMinutes(-5);
                
                for (int i = _notifications.Count - 1; i >= 0; i--)
                {
                    if (_notifications[i].Timestamp < cutoffTime)
                    {
                        _notifications.RemoveAt(i);
                    }
                }
            });
        }

        // Quick notification methods
        public static void Success(string message) => Instance.ShowSuccess("نجح", message);
        public static void Error(string message) => Instance.ShowError("خطأ", message);
        public static void Warning(string message) => Instance.ShowWarning("تحذير", message);
        public static void Info(string message) => Instance.ShowInfo("معلومات", message);

        // Detailed notification methods
        public static void Success(string title, string message) => Instance.ShowSuccess(title, message);
        public static void Error(string title, string message) => Instance.ShowError(title, message);
        public static void Warning(string title, string message) => Instance.ShowWarning(title, message);
        public static void Info(string title, string message) => Instance.ShowInfo(title, message);
    }

    // Extension methods for common operations
    public static class NotificationExtensions
    {
        public static void NotifySuccess(this string message)
        {
            NotificationService.Success(message);
        }

        public static void NotifyError(this string message)
        {
            NotificationService.Error(message);
        }

        public static void NotifyWarning(this string message)
        {
            NotificationService.Warning(message);
        }

        public static void NotifyInfo(this string message)
        {
            NotificationService.Info(message);
        }
    }
}
