using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Services;
using DateFactoryApp.Views;

namespace DateFactoryApp.ViewModels
{
    public partial class LoginViewModel : ObservableObject
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private string username = "admin";

        [ObservableProperty]
        private string password = string.Empty;

        [ObservableProperty]
        private bool rememberMe = false;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool hasError = false;

        public LoginViewModel(IUserService userService)
        {
            _userService = userService;
            LoadSavedCredentials();
        }

        [RelayCommand]
        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                ErrorMessage = string.Empty;

                if (string.IsNullOrWhiteSpace(Username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    return;
                }

                if (string.IsNullOrWhiteSpace(Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                var user = await _userService.AuthenticateAsync(Username, Password);

                if (user != null)
                {
                    if (RememberMe)
                    {
                        SaveCredentials();
                    }
                    else
                    {
                        ClearSavedCredentials();
                    }

                    // Open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();

                    // Close login window
                    Application.Current.Windows.OfType<LoginWindow>().FirstOrDefault()?.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    Password = string.Empty;
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ShowConnectionSettings()
        {
            // TODO: Implement connection settings dialog
            MessageBox.Show("إعدادات الاتصال ستكون متاحة قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        private void LoadSavedCredentials()
        {
            try
            {
                var settingsPath = GetCredentialsPath();
                if (File.Exists(settingsPath))
                {
                    var lines = File.ReadAllLines(settingsPath);
                    if (lines.Length >= 2)
                    {
                        Username = DecryptString(lines[0]);
                        RememberMe = bool.TryParse(lines[1], out bool remember) && remember;
                    }
                }
            }
            catch
            {
                // Ignore errors when loading saved credentials
            }
        }

        private void SaveCredentials()
        {
            try
            {
                var settingsPath = GetCredentialsPath();
                var directory = Path.GetDirectoryName(settingsPath);

                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var lines = new[]
                {
                    EncryptString(Username),
                    RememberMe.ToString()
                };

                File.WriteAllLines(settingsPath, lines);
            }
            catch
            {
                // Ignore errors when saving credentials
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                var settingsPath = GetCredentialsPath();
                if (File.Exists(settingsPath))
                {
                    File.Delete(settingsPath);
                }
            }
            catch
            {
                // Ignore errors when clearing credentials
            }
        }

        private string GetCredentialsPath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "DateFactory", "credentials.dat");
        }

        private string EncryptString(string text)
        {
            // Simple base64 encoding for demo purposes
            // In production, use proper encryption
            var bytes = System.Text.Encoding.UTF8.GetBytes(text);
            return Convert.ToBase64String(bytes);
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                var bytes = Convert.FromBase64String(encryptedText);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
