using System.Windows;
using System.Windows.Controls;
using DateFactoryApp.Helpers;

namespace DateFactoryApp.Views
{
    public partial class NotificationPanel : UserControl
    {
        public NotificationPanel()
        {
            InitializeComponent();
        }

        private void CloseNotification_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is NotificationItem notification)
            {
                NotificationService.Instance.HideNotification(notification);
            }
        }
    }
}
