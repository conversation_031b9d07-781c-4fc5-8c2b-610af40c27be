# Test Application Script
# نص اختبار تطبيق إدارة مصنع التمور

Write-Host ""
Write-Host "================================================" -ForegroundColor Green
Write-Host "    🧪 اختبار تطبيق إدارة مصنع التمور" -ForegroundColor Yellow
Write-Host "    📋 فحص سريع للتطبيق" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

Write-Host "🔍 جاري فحص الملفات المطلوبة..." -ForegroundColor Cyan

# Check if executable exists
$exePath = ".\bin\Release\net8.0-windows\DateFactoryApp.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "❌ الملف التنفيذي غير موجود" -ForegroundColor Red
    Write-Host "🔨 جاري بناء التطبيق..." -ForegroundColor Yellow
    
    try {
        dotnet build DateFactoryApp.csproj --configuration Release
        if ($LASTEXITCODE -ne 0) {
            throw "فشل في بناء التطبيق"
        }
        Write-Host "✅ تم بناء التطبيق بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "❌ فشل في بناء التطبيق: $_" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
} else {
    Write-Host "✅ الملف التنفيذي موجود" -ForegroundColor Green
}

# Check database
if (Test-Path "DateFactory.db") {
    Write-Host "✅ قاعدة البيانات موجودة" -ForegroundColor Green
} else {
    Write-Host "⚠️  قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً" -ForegroundColor Yellow
}

# Check logs directory
if (-not (Test-Path "logs")) {
    Write-Host "📁 إنشاء مجلد السجلات..." -ForegroundColor Cyan
    New-Item -ItemType Directory -Path "logs" -Force | Out-Null
}

Write-Host ""
Write-Host "🚀 جاري تشغيل التطبيق..." -ForegroundColor Green
Write-Host ""
Write-Host "بيانات الدخول الافتراضية:" -ForegroundColor Cyan
Write-Host "اسم المستخدم: admin" -ForegroundColor White
Write-Host "كلمة المرور: admin123" -ForegroundColor White
Write-Host ""

# Start the application
try {
    Start-Process -FilePath $exePath -Wait
    Write-Host "✅ تم إغلاق التطبيق بنجاح!" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "📝 لمراجعة السجلات، تحقق من مجلد logs" -ForegroundColor Cyan
Write-Host "🔧 لإعادة بناء التطبيق، استخدم run.ps1" -ForegroundColor Cyan
Write-Host ""
Read-Host "اضغط Enter للخروج"
