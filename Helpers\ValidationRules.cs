using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Controls;

namespace DateFactoryApp.Helpers
{
    /// <summary>
    /// قاعدة التحقق من الحقول المطلوبة
    /// </summary>
    public class RequiredFieldValidationRule : ValidationRule
    {
        public string ErrorMessage { get; set; } = "هذا الحقل مطلوب";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult(false, ErrorMessage);
            }
            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من البريد الإلكتروني
    /// </summary>
    public class EmailValidationRule : ValidationRule
    {
        public string ErrorMessage { get; set; } = "البريد الإلكتروني غير صحيح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.ValidResult; // Allow empty for optional fields
            }

            string email = value.ToString()!;
            string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            
            if (!Regex.IsMatch(email, pattern))
            {
                return new ValidationResult(false, ErrorMessage);
            }
            
            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من رقم الهاتف
    /// </summary>
    public class PhoneValidationRule : ValidationRule
    {
        public string ErrorMessage { get; set; } = "رقم الهاتف غير صحيح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.ValidResult; // Allow empty for optional fields
            }

            string phone = value.ToString()!;
            // Saudi phone number pattern
            string pattern = @"^(\+966|966|0)?[5][0-9]{8}$";
            
            if (!Regex.IsMatch(phone, pattern))
            {
                return new ValidationResult(false, ErrorMessage);
            }
            
            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من الأرقام الموجبة
    /// </summary>
    public class PositiveNumberValidationRule : ValidationRule
    {
        public string ErrorMessage { get; set; } = "يجب أن يكون الرقم أكبر من الصفر";
        public bool AllowZero { get; set; } = false;

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult(false, "هذا الحقل مطلوب");
            }

            if (decimal.TryParse(value.ToString(), out decimal number))
            {
                if (AllowZero && number >= 0)
                {
                    return ValidationResult.ValidResult;
                }
                else if (!AllowZero && number > 0)
                {
                    return ValidationResult.ValidResult;
                }
            }

            return new ValidationResult(false, ErrorMessage);
        }
    }

    /// <summary>
    /// قاعدة التحقق من طول النص
    /// </summary>
    public class StringLengthValidationRule : ValidationRule
    {
        public int MinLength { get; set; } = 0;
        public int MaxLength { get; set; } = int.MaxValue;
        public string ErrorMessage { get; set; } = "طول النص غير صحيح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            string text = value?.ToString() ?? string.Empty;
            
            if (text.Length < MinLength)
            {
                return new ValidationResult(false, $"يجب أن يكون النص أطول من {MinLength} أحرف");
            }
            
            if (text.Length > MaxLength)
            {
                return new ValidationResult(false, $"يجب أن يكون النص أقصر من {MaxLength} حرف");
            }
            
            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من كلمة المرور
    /// </summary>
    public class PasswordValidationRule : ValidationRule
    {
        public int MinLength { get; set; } = 6;
        public bool RequireUppercase { get; set; } = false;
        public bool RequireLowercase { get; set; } = false;
        public bool RequireDigit { get; set; } = false;
        public bool RequireSpecialChar { get; set; } = false;

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult(false, "كلمة المرور مطلوبة");
            }

            string password = value.ToString()!;

            if (password.Length < MinLength)
            {
                return new ValidationResult(false, $"كلمة المرور يجب أن تكون {MinLength} أحرف على الأقل");
            }

            if (RequireUppercase && !Regex.IsMatch(password, @"[A-Z]"))
            {
                return new ValidationResult(false, "كلمة المرور يجب أن تحتوي على حرف كبير");
            }

            if (RequireLowercase && !Regex.IsMatch(password, @"[a-z]"))
            {
                return new ValidationResult(false, "كلمة المرور يجب أن تحتوي على حرف صغير");
            }

            if (RequireDigit && !Regex.IsMatch(password, @"[0-9]"))
            {
                return new ValidationResult(false, "كلمة المرور يجب أن تحتوي على رقم");
            }

            if (RequireSpecialChar && !Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]"))
            {
                return new ValidationResult(false, "كلمة المرور يجب أن تحتوي على رمز خاص");
            }

            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من النطاق الرقمي
    /// </summary>
    public class NumericRangeValidationRule : ValidationRule
    {
        public decimal MinValue { get; set; } = decimal.MinValue;
        public decimal MaxValue { get; set; } = decimal.MaxValue;
        public string ErrorMessage { get; set; } = "القيمة خارج النطاق المسموح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.ValidResult; // Allow empty for optional fields
            }

            if (decimal.TryParse(value.ToString(), out decimal number))
            {
                if (number >= MinValue && number <= MaxValue)
                {
                    return ValidationResult.ValidResult;
                }
            }

            return new ValidationResult(false, $"{ErrorMessage} ({MinValue} - {MaxValue})");
        }
    }

    /// <summary>
    /// قاعدة التحقق من التاريخ
    /// </summary>
    public class DateValidationRule : ValidationRule
    {
        public DateTime? MinDate { get; set; }
        public DateTime? MaxDate { get; set; }
        public string ErrorMessage { get; set; } = "التاريخ غير صحيح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return ValidationResult.ValidResult; // Allow empty for optional fields
            }

            DateTime date;
            if (value is DateTime dateTime)
            {
                date = dateTime;
            }
            else if (!DateTime.TryParse(value.ToString(), out date))
            {
                return new ValidationResult(false, "تنسيق التاريخ غير صحيح");
            }

            if (MinDate.HasValue && date < MinDate.Value)
            {
                return new ValidationResult(false, $"التاريخ يجب أن يكون بعد {MinDate.Value:yyyy/MM/dd}");
            }

            if (MaxDate.HasValue && date > MaxDate.Value)
            {
                return new ValidationResult(false, $"التاريخ يجب أن يكون قبل {MaxDate.Value:yyyy/MM/dd}");
            }

            return ValidationResult.ValidResult;
        }
    }

    /// <summary>
    /// قاعدة التحقق من الرقم الضريبي السعودي
    /// </summary>
    public class SaudiTaxNumberValidationRule : ValidationRule
    {
        public string ErrorMessage { get; set; } = "الرقم الضريبي غير صحيح";

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.ValidResult; // Allow empty for optional fields
            }

            string taxNumber = value.ToString()!.Replace("-", "").Replace(" ", "");
            
            // Saudi tax number should be 15 digits
            if (taxNumber.Length != 15 || !Regex.IsMatch(taxNumber, @"^\d{15}$"))
            {
                return new ValidationResult(false, ErrorMessage);
            }

            return ValidationResult.ValidResult;
        }
    }
}
