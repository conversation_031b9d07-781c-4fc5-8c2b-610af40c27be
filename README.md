# 🏭 نظام إدارة مصنع ومحل التمور المتقدم

نظام شامل ومتطور لإدارة جميع عمليات مصنع ومحل التمور باستخدام أحدث التقنيات مع C# WPF وتصميم Material Design الأنيق.

## ✨ المميزات الرئيسية المحدثة

### 📊 إدارة شاملة ومتقدمة
- **إدارة المنتجات المتطورة**: تصنيف ذكي وإدارة أنواع التمور المختلفة (خام، معالج، معبأ) مع تتبع دقيق
- **نظام مبيعات متكامل**: فواتير احترافية مع طرق دفع متعددة وطباعة فورية
- **إدارة مخزون ذكية**: تتبع المخزون مع تنبيهات ذكية وتقارير مفصلة
- **إدارة العملاء المتقدمة**: قاعدة بيانات شاملة مع تتبع تاريخ المبيعات والتفضيلات
- **إدارة الموردين**: متابعة شاملة للموردين وأوامر الشراء مع تقييم الأداء
- **إدارة الإنتاج المتطورة**: تسجيل مفصل لعمليات التصنيع والتعبئة مع مراقبة الجودة

### 🎨 واجهة مستخدم عصرية ومحسنة
- **Material Design المتقدم**: تصميم عصري وأنيق مع تأثيرات بصرية جذابة
- **الوضع الليلي/النهاري**: تبديل سلس بين الأوضاع مع حفظ التفضيلات
- **دعم كامل للغة العربية**: واجهة مصممة خصيصاً للغة العربية مع اتجاه النص الصحيح
- **تجربة مستخدم متميزة**: تنقل سهل وبديهي مع رسوم متحركة سلسة
- **نافذة ترحيب متحركة**: شاشة بداية أنيقة مع تأثيرات بصرية
- **نظام إشعارات متطور**: إشعارات ملونة وتفاعلية مع إخفاء تلقائي

### 🔐 نظام الصلاحيات
- **مدير**: صلاحيات كاملة
- **مشرف**: صلاحيات متوسطة
- **كاشير**: صلاحيات محدودة للمبيعات
- **عامل**: صلاحيات أساسية

### 📈 التقارير والإحصائيات
- **تقارير المبيعات**: يومية، أسبوعية، شهرية
- **تقارير المخزون**: حالة المخزون والمنتجات المنخفضة
- **تقارير الأرباح**: تحليل الربحية
- **تقارير العملاء والموردين**: أداء العملاء والموردين

### 💾 قاعدة البيانات المحلية
- **SQLite**: قاعدة بيانات محلية لا تحتاج تثبيت
- **ملف واحد**: سهولة النقل والنسخ الاحتياطي
- **أداء عالي**: استجابة سريعة

### ☁️ المزامنة التلقائية
- **مزامنة سحابية**: رفع البيانات تلقائياً عند توفر الإنترنت
- **العمل بدون إنترنت**: التطبيق يعمل بالكامل بدون إنترنت
- **تتبع التغييرات**: مزامنة البيانات المحدثة فقط

### 🔄 النسخ الاحتياطي
- **نسخ احتياطية تلقائية**: حفظ تلقائي للبيانات
- **استعادة سهلة**: استعادة البيانات بنقرة واحدة
- **ملفات مضغوطة**: توفير مساحة التخزين

## 🚀 متطلبات التشغيل

- **نظام التشغيل**: Windows 10/11
- **إطار العمل**: .NET 8.0
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 (الحد الأدنى)

## 📦 التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
# تأكد من تثبيت .NET 8.0 Runtime
# يمكن تحميله من: https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. استنساخ المشروع
```bash
git clone https://github.com/your-repo/DateFactoryApp.git
cd DateFactoryApp
```

### 3. استعادة الحزم
```bash
dotnet restore
```

### 4. بناء المشروع
```bash
dotnet build
```

### 5. تشغيل التطبيق
```bash
dotnet run
```

## 🔑 بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📁 هيكل المشروع

```
DateFactoryApp/
├── Models/              # نماذج البيانات
├── ViewModels/          # منطق العرض
├── Views/               # واجهات المستخدم
├── Services/            # الخدمات والمنطق التجاري
├── Data/                # قاعدة البيانات والسياق
├── Resources/           # الموارد والأنماط
└── Helpers/             # المساعدات والأدوات
```

## 🛠️ التقنيات المستخدمة

- **C# 12**: لغة البرمجة الأساسية
- **WPF**: إطار عمل واجهة المستخدم
- **Material Design In XAML**: مكتبة التصميم
- **Entity Framework Core**: ORM لقاعدة البيانات
- **SQLite**: قاعدة البيانات المحلية
- **CommunityToolkit.Mvvm**: نمط MVVM
- **Serilog**: تسجيل الأحداث

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- **Users**: المستخدمين والصلاحيات
- **Products**: المنتجات والتصنيفات
- **Sales**: المبيعات والفواتير
- **Customers**: العملاء
- **Suppliers**: الموردين
- **InventoryTransactions**: حركات المخزون
- **ProductionRecords**: سجلات الإنتاج

## 🔧 الإعدادات والتخصيص

### إعدادات الثيم
- تغيير الألوان الأساسية والثانوية
- التبديل بين الوضع الليلي والنهاري
- حفظ التفضيلات تلقائياً

### إعدادات المزامنة
- تكوين نقطة النهاية للمزامنة
- تفعيل/إلغاء المزامنة التلقائية
- إعدادات الأمان والمصادقة

### إعدادات النسخ الاحتياطي
- تحديد مسار النسخ الاحتياطية
- جدولة النسخ التلقائية
- إعدادات الضغط والتشفير

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في قاعدة البيانات**
   - تأكد من وجود ملف `DateFactory.db`
   - تحقق من صلاحيات الكتابة في المجلد

2. **مشاكل المزامنة**
   - تحقق من اتصال الإنترنت
   - تأكد من صحة إعدادات المزامنة

3. **مشاكل الواجهة**
   - تأكد من تثبيت .NET 8.0 Runtime
   - أعد تشغيل التطبيق

## 🆕 المميزات الجديدة والمحسنة

### 🔒 الأمان والمراقبة المتقدمة
- **تسجيل الأحداث الشامل**: نظام audit log متطور لتتبع جميع العمليات
- **تشفير متقدم**: حماية قوية لكلمات المرور والبيانات الحساسة
- **نظام التحقق المتطور**: قواعد تحقق ذكية للبيانات المدخلة
- **مراقبة النشاط**: تتبع تفصيلي لجميع أنشطة المستخدمين

### 🖨️ الطباعة والتقارير المحسنة
- **طباعة احترافية**: فواتير وإيصالات بتصميم احترافي عالي الجودة
- **تقارير تفاعلية**: تقارير مفصلة مع رسوم بيانية ملونة
- **تصدير متعدد الصيغ**: دعم CSV، Excel، PDF، JSON
- **قوالب مخصصة**: إمكانية تخصيص تصميم الفواتير والتقارير

### ⚙️ الإعدادات والتخصيص المتقدم
- **إعدادات شاملة**: تحكم كامل في سلوك النظام
- **حفظ التفضيلات**: حفظ تلقائي لجميع إعدادات المستخدم
- **استيراد/تصدير الإعدادات**: نقل سهل للإعدادات بين الأجهزة
- **إعادة تعيين ذكية**: استعادة الإعدادات الافتراضية بنقرة واحدة

### 🔄 التحديثات والصيانة التلقائية
- **فحص التحديثات التلقائي**: فحص ذكي للإصدارات الجديدة
- **تحديث آمن**: تحميل وتثبيت آمن مع نسخ احتياطي تلقائي
- **إشعارات ذكية**: تنبيهات غير مزعجة للتحديثات المتوفرة
- **استعادة تلقائية**: حماية من فشل التحديث مع استعادة تلقائية

### 🎨 تحسينات الواجهة والتجربة
- **نافذة ترحيب متحركة**: شاشة بداية أنيقة مع تأثيرات بصرية جذابة
- **نظام إشعارات متطور**: إشعارات ملونة وتفاعلية مع إخفاء تلقائي
- **نافذة "حول البرنامج"**: معلومات شاملة عن التطبيق والشركة
- **تأثيرات بصرية محسنة**: رسوم متحركة سلسة وتأثيرات ظل متقدمة

### 💾 النسخ الاحتياطي المتطور
- **ضغط ذكي**: نسخ احتياطية مضغوطة لتوفير المساحة
- **تشفير البيانات**: حماية النسخ الاحتياطية بتشفير قوي
- **جدولة متقدمة**: نسخ احتياطي تلقائي مع إعدادات مرنة
- **تنظيف تلقائي**: حذف النسخ القديمة تلقائياً

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراسلة فريق التطوير
- مراجعة الوثائق التقنية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📝 سجل التغييرات

راجع [CHANGELOG.md](CHANGELOG.md) لمعرفة آخر التحديثات والتحسينات.

---

**© 2024 نظام إدارة مصنع ومحل التمور - جميع الحقوق محفوظة**
