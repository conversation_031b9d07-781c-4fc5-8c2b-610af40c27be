<UserControl x:Class="DateFactoryApp.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="الإعدادات" Style="{StaticResource PageTitle}"/>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,16,0,0">
                
                <!-- General Settings -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="الإعدادات العامة" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox materialDesign:HintAssist.Hint="اسم الشركة"
                                       Text="{Binding CompanyName}" Margin="0,0,0,8"/>
                                <TextBox materialDesign:HintAssist.Hint="عنوان الشركة"
                                       Text="{Binding CompanyAddress}" Margin="0,0,0,8"/>
                                <TextBox materialDesign:HintAssist.Hint="هاتف الشركة"
                                       Text="{Binding CompanyPhone}" Margin="0,0,0,8"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBox materialDesign:HintAssist.Hint="بريد الشركة الإلكتروني"
                                       Text="{Binding CompanyEmail}" Margin="0,0,0,8"/>
                                <TextBox materialDesign:HintAssist.Hint="الرقم الضريبي"
                                       Text="{Binding TaxNumber}" Margin="0,0,0,8"/>
                                <ComboBox materialDesign:HintAssist.Hint="العملة الافتراضية"
                                        SelectedItem="{Binding DefaultCurrency}" Margin="0,0,0,8"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Theme Settings -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="إعدادات المظهر" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <TextBlock Text="المظهر:" VerticalAlignment="Center" Margin="0,0,16,0"/>
                            <RadioButton Content="فاتح" IsChecked="{Binding IsLightTheme}" Margin="0,0,16,0"/>
                            <RadioButton Content="داكن" IsChecked="{Binding IsDarkTheme}"/>
                        </StackPanel>
                        
                        <ComboBox materialDesign:HintAssist.Hint="اللون الأساسي"
                                SelectedItem="{Binding PrimaryColor}" Width="200" HorizontalAlignment="Right"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Backup Settings -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="إعدادات النسخ الاحتياطي" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>
                        
                        <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي" 
                                IsChecked="{Binding AutoBackupEnabled}" Margin="0,0,0,8"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <TextBlock Text="تكرار النسخ الاحتياطي:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox SelectedItem="{Binding BackupFrequency}" Width="150"/>
                        </StackPanel>
                        
                        <TextBox materialDesign:HintAssist.Hint="مجلد النسخ الاحتياطي"
                               Text="{Binding BackupPath}" Margin="0,0,0,8"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                            <Button Content="إنشاء نسخة احتياطية الآن" Command="{Binding CreateBackupCommand}" Margin="0,0,8,0"/>
                            <Button Content="استعادة من نسخة احتياطية" Command="{Binding RestoreBackupCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- User Management -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="إدارة المستخدمين" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>
                        
                        <DataGrid ItemsSource="{Binding Users}" SelectedItem="{Binding SelectedUser}"
                                Style="{StaticResource CustomDataGrid}" MaxHeight="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="150"/>
                                <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                                <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="100"/>
                                <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      ToolTip="تعديل">
                                                    <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      Command="{Binding DataContext.DeleteUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      ToolTip="حذف">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                        
                        <Button Content="إضافة مستخدم جديد" Command="{Binding AddUserCommand}" 
                              Margin="0,8,0,0" HorizontalAlignment="Right"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Save Button -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                    <Button Content="حفظ الإعدادات" Command="{Binding SaveSettingsCommand}" 
                          Style="{StaticResource PrimaryButton}" Margin="0,0,8,0"/>
                    <Button Content="إعادة تعيين" Command="{Binding ResetSettingsCommand}" 
                          Style="{StaticResource SecondaryButton}"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
