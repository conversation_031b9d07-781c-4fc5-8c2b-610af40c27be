@echo off
chcp 65001 > nul
title نظام إدارة مصنع ومحل التمور المتقدم - الإصدار 1.0

echo.
echo ================================================
echo    🏭 نظام إدارة مصنع ومحل التمور المتقدم
echo    📦 الإصدار 1.0.1 - المحسن والمتطور
echo    © 2024 شركة التمور المتقدمة
echo ================================================
echo.

REM Check if .NET 8.0 is installed
echo 🔍 جاري التحقق من متطلبات النظام...
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: .NET 8.0 Runtime غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 8.0 Runtime من:
    echo    https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo 💡 أو استخدم الأمر التالي لتثبيت .NET 8.0:
    echo    winget install Microsoft.DotNet.Runtime.8
    echo.
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ تم العثور على .NET Runtime - الإصدار: %DOTNET_VERSION%
echo.

REM Check for required packages
echo 🔍 جاري التحقق من الحزم المطلوبة...
echo 📦 جاري استعادة الحزم...
dotnet restore --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في استعادة الحزم
    pause
    exit /b 1
)
echo ✅ تم التحقق من الحزم بنجاح
echo.

REM Build the project
echo 🔨 جاري بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء المشروع
    echo.
    echo 🔧 جاري المحاولة مع تفاصيل أكثر...
    dotnet build --configuration Release
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

REM Create logs directory if it doesn't exist
if not exist "Logs" mkdir Logs

REM Create backups directory if it doesn't exist
if not exist "Backups" mkdir Backups

REM Create reports directory if it doesn't exist
if not exist "Reports" mkdir Reports

REM Run the application
echo 🚀 جاري تشغيل التطبيق...
echo.
echo ================================================
echo    🎯 مميزات الإصدار الجديد:
echo    ✨ نافذة ترحيب متحركة
echo    🔔 نظام إشعارات متطور
echo    🔒 أمان وتشفير متقدم
echo    🖨️ طباعة احترافية
echo    ⚙️ إعدادات شاملة
echo    🔄 تحديثات تلقائية
echo ================================================
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔐 كلمة المرور: admin123
echo.

dotnet run --configuration Release

echo.
echo 👋 تم إغلاق التطبيق بنجاح
echo 📞 للدعم الفني: <EMAIL>
echo 🌐 الموقع: www.datefactory.com
echo.
pause
