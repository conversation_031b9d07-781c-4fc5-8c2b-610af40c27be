<UserControl x:Class="DateFactoryApp.Views.SuppliersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة الموردين" Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox Width="300" 
                           materialDesign:HintAssist.Hint="البحث في الموردين..."
                           Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                    <Button Content="بحث" Command="{Binding SearchCommand}" Margin="8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="مورد جديد" Command="{Binding AddSupplierCommand}">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Suppliers List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة الموردين" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Suppliers}" 
                            SelectedItem="{Binding SelectedSupplier}"
                            Style="{StaticResource CustomDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="80"/>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                            <DataGridTextColumn Header="شروط الدفع" Binding="{Binding PaymentTerms}" Width="80"/>
                            <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.EditSupplierCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="تعديل">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.DeleteSupplierCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="حذف">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Supplier Details/Edit Form -->
            <materialDesign:Card Grid.Column="1">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="تفاصيل المورد" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>

                        <TextBox materialDesign:HintAssist.Hint="اسم المورد *"
                               Text="{Binding EditingSupplier.Name}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="كود المورد"
                               Text="{Binding EditingSupplier.Code}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                               Text="{Binding EditingSupplier.Phone}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="البريد الإلكتروني"
                               Text="{Binding EditingSupplier.Email}" Margin="0,0,0,8"/>

                        <ComboBox materialDesign:HintAssist.Hint="نوع المورد"
                                SelectedItem="{Binding EditingSupplier.Type}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="العنوان"
                               Text="{Binding EditingSupplier.Address}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="شروط الدفع (بالأيام)"
                               Text="{Binding EditingSupplier.PaymentTerms}" Margin="0,0,0,8"/>

                        <CheckBox Content="نشط" IsChecked="{Binding EditingSupplier.IsActive}" Margin="0,8"/>

                        <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                            <Button Content="حفظ" Command="{Binding SaveSupplierCommand}" Margin="0,0,8,0"/>
                            <Button Content="إلغاء" Command="{Binding CancelEditCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
