using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface IBackupService
    {
        Task<bool> CreateBackupAsync(string backupPath);
        Task<bool> RestoreBackupAsync(string backupPath);
        Task<bool> CreateAutoBackupAsync();
        Task<IEnumerable<BackupInfo>> GetBackupHistoryAsync();
        Task<bool> DeleteBackupAsync(string backupPath);
        Task<long> GetDatabaseSizeAsync();
        Task<bool> ValidateBackupAsync(string backupPath);
        string GetDefaultBackupPath();
    }

    public class BackupInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public long FileSize { get; set; }
        public string FormattedSize { get; set; } = string.Empty;
        public bool IsValid { get; set; }
    }
}
