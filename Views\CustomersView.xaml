<UserControl x:Class="DateFactoryApp.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة العملاء" Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox Width="300" 
                           materialDesign:HintAssist.Hint="البحث في العملاء..."
                           Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                    <Button Content="بحث" Command="{Binding SearchCommand}" Margin="8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="عميل جديد" Command="{Binding AddCustomerCommand}">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Customers List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة العملاء" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Customers}" 
                            SelectedItem="{Binding SelectedCustomer}"
                            Style="{StaticResource CustomDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="80"/>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="80"/>
                            <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat='{}{0:C}'}" Width="100"/>
                            <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.EditCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="تعديل">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.DeleteCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="حذف">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Customer Details/Edit Form -->
            <materialDesign:Card Grid.Column="1">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="تفاصيل العميل" Style="{StaticResource SectionTitle}" Margin="0,0,0,16"/>

                        <TextBox materialDesign:HintAssist.Hint="اسم العميل *"
                               Text="{Binding EditingCustomer.Name}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="كود العميل"
                               Text="{Binding EditingCustomer.Code}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                               Text="{Binding EditingCustomer.Phone}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="البريد الإلكتروني"
                               Text="{Binding EditingCustomer.Email}" Margin="0,0,0,8"/>

                        <ComboBox materialDesign:HintAssist.Hint="نوع العميل"
                                SelectedItem="{Binding EditingCustomer.Type}" Margin="0,0,0,8"/>

                        <TextBox materialDesign:HintAssist.Hint="العنوان"
                               Text="{Binding EditingCustomer.Address}" Margin="0,0,0,8"/>

                        <CheckBox Content="نشط" IsChecked="{Binding EditingCustomer.IsActive}" Margin="0,8"/>

                        <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                            <Button Content="حفظ" Command="{Binding SaveCustomerCommand}" Margin="0,0,8,0"/>
                            <Button Content="إلغاء" Command="{Binding CancelEditCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
