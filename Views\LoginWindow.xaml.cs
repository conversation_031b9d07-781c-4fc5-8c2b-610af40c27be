using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using DateFactoryApp.Services;
using DateFactoryApp.Helpers;

namespace DateFactoryApp.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IUserService _userService;

        public LoginWindow()
        {
            InitializeComponent();

            // Get user service
            _userService = App.GetService<IUserService>();

            // Focus on username textbox when window loads
            Loaded += (s, e) => UsernameTextBox.Focus();

            // Handle Enter key press
            KeyDown += LoginWindow_KeyDown;

            // Make window draggable
            MouseLeftButtonDown += (s, e) => DragMove();
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, new RoutedEventArgs());
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLoginAsync();
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                // Show loading
                ShowLoading(true);
                HideError();

                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                // Validate input
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                // Simulate network delay for better UX
                await Task.Delay(1000);

                // Attempt login
                var user = await _userService.AuthenticateAsync(username, password);

                if (user != null)
                {
                    // Save remember me preference
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        // TODO: Implement remember me functionality
                        // Properties.Settings.Default.RememberUsername = username;
                        // Properties.Settings.Default.Save();
                    }

                    // Show success notification
                    NotificationService.Success("تم تسجيل الدخول بنجاح", $"مرحباً {user.FullName}");

                    // Login successful - open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ShowLoading(bool show)
        {
            LoadingPanel.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoginButton.IsEnabled = !show;
            UsernameTextBox.IsEnabled = !show;
            PasswordBox.IsEnabled = !show;
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorCard.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorCard.Visibility = Visibility.Collapsed;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}
