using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class Sale : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public DateTime SaleDate { get; set; } = DateTime.Now;

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } = 0;

        [Required]
        public PaymentMethod PaymentMethod { get; set; }

        [Required]
        public SaleStatus Status { get; set; } = SaleStatus.Completed;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public bool IsPrinted { get; set; } = false;

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }

    public class SaleItem : BaseEntity
    {
        [Required]
        public int SaleId { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        // Navigation properties
        public virtual Sale Sale { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    public enum PaymentMethod
    {
        Cash = 1,       // نقدي
        Credit = 2,     // آجل
        Card = 3,       // بطاقة
        Transfer = 4,   // تحويل
        Check = 5       // شيك
    }

    public enum SaleStatus
    {
        Draft = 1,      // مسودة
        Completed = 2,  // مكتملة
        Cancelled = 3,  // ملغية
        Returned = 4    // مرتجعة
    }
}
