<UserControl x:Class="DateFactoryApp.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة المبيعات" Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Date Filters -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <DatePicker materialDesign:HintAssist.Hint="من تاريخ"
                              SelectedDate="{Binding StartDate}" Width="150" Margin="0,0,8,0"/>
                    <DatePicker materialDesign:HintAssist.Hint="إلى تاريخ"
                              SelectedDate="{Binding EndDate}" Width="150" Margin="0,0,8,0"/>
                    <Button Content="تصفية" Style="{StaticResource SecondaryButton}"
                          Command="{Binding FilterSalesCommand}" Margin="8,0"/>
                    <Button Content="اليوم" Style="{StaticResource SecondaryButton}"
                          Command="{Binding ShowTodaySalesCommand}" Margin="8,0"/>
                    <Button Content="هذا الأسبوع" Style="{StaticResource SecondaryButton}"
                          Command="{Binding ShowThisWeekSalesCommand}" Margin="8,0"/>
                    <Button Content="هذا الشهر" Style="{StaticResource SecondaryButton}"
                          Command="{Binding ShowThisMonthSalesCommand}" Margin="8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="فاتورة جديدة" Style="{StaticResource PrimaryButton}"
                          Command="{Binding StartNewSaleCommand}">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <!-- Sales List View -->
            <materialDesign:Card Visibility="{Binding IsNewSaleMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة المبيعات" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Sales}" 
                            SelectedItem="{Binding SelectedSale}"
                            Style="{StaticResource CustomDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding Customer.Name}" Width="150"/>
                            <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat='{}{0:C}'}" Width="120"/>
                            <DataGridTextColumn Header="المدفوع" Binding="{Binding PaidAmount, StringFormat='{}{0:C}'}" Width="100"/>
                            <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat='{}{0:C}'}" Width="100"/>
                            <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.PrintSaleCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="طباعة">
                                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.DeleteSaleCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="حذف">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- New Sale Form -->
            <Grid Visibility="{Binding IsNewSaleMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Sale Header -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="رقم الفاتورة" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <TextBlock Text="{Binding CurrentSale.InvoiceNumber}" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="التاريخ" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <DatePicker SelectedDate="{Binding CurrentSale.SaleDate}" Width="150"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="العميل" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <ComboBox ItemsSource="{Binding Customers}"
                                    SelectedValue="{Binding CurrentSale.CustomerId}"
                                    SelectedValuePath="Id"
                                    DisplayMemberPath="Name"
                                    Width="200"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                            <Button Content="حفظ الفاتورة" Style="{StaticResource SuccessButton}"
                                  Command="{Binding SaveSaleCommand}"/>
                            <Button Content="إلغاء" Style="{StaticResource SecondaryButton}"
                                  Command="{Binding CancelSaleCommand}" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!-- Sale Items -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Items List -->
                    <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="أصناف الفاتورة" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                            <DataGrid Grid.Row="1" ItemsSource="{Binding CurrentSaleItems}"
                                    Style="{StaticResource CustomDataGrid}">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المنتج" Binding="{Binding Product.Name}" Width="200"/>
                                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                                    <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat='{}{0:C}'}" Width="100"/>
                                    <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalPrice, StringFormat='{}{0:C}'}" Width="120"/>
                                    <DataGridTemplateColumn Header="حذف" Width="60">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      Command="{Binding DataContext.RemoveProductFromSaleCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding}">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Add Product Panel -->
                    <materialDesign:Card Grid.Column="1" Margin="8,0,0,0">
                        <StackPanel Style="{StaticResource FormPanel}">
                            <TextBlock Text="إضافة منتج" Style="{StaticResource SectionTitle}"/>

                            <ComboBox materialDesign:HintAssist.Hint="اختر المنتج"
                                    ItemsSource="{Binding Products}"
                                    SelectedItem="{Binding SelectedProduct}"
                                    DisplayMemberPath="Name"
                                    Style="{StaticResource FormComboBox}"/>

                            <TextBox materialDesign:HintAssist.Hint="الكمية"
                                   Text="{Binding Quantity}"
                                   Style="{StaticResource FormTextBox}"/>

                            <TextBox materialDesign:HintAssist.Hint="السعر"
                                   Text="{Binding UnitPrice}"
                                   Style="{StaticResource FormTextBox}"/>

                            <Button Content="إضافة للفاتورة" Style="{StaticResource PrimaryButton}"
                                  Command="{Binding AddProductToSaleCommand}"/>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- Sale Summary -->
                <materialDesign:Card Grid.Row="2" Margin="0,8,0,0" Padding="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <ComboBox materialDesign:HintAssist.Hint="طريقة الدفع"
                                    SelectedItem="{Binding CurrentSale.PaymentMethod}"
                                    Width="150" Margin="0,0,16,0"/>
                            <TextBox materialDesign:HintAssist.Hint="خصم"
                                   Text="{Binding CurrentSale.DiscountAmount}"
                                   Width="100" Margin="0,0,16,0"/>
                            <TextBox materialDesign:HintAssist.Hint="ضريبة"
                                   Text="{Binding CurrentSale.TaxAmount}"
                                   Width="100"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                <TextBlock Text="المجموع الفرعي: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding CurrentSale.SubTotal, StringFormat='{}{0:C}'}" FontWeight="Bold"/>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                <TextBlock Text="الإجمالي: " FontWeight="Medium" FontSize="16"/>
                                <TextBlock Text="{Binding CurrentSale.TotalAmount, StringFormat='{}{0:C}'}" 
                                         FontWeight="Bold" FontSize="16" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" Background="Black" Opacity="0.5" 
            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="50" Height="50" IsIndeterminate="True"/>
                <TextBlock Text="جاري التحميل..." Foreground="White" HorizontalAlignment="Center" Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
