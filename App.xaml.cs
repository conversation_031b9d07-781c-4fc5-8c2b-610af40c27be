using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DateFactoryApp.Data;
using DateFactoryApp.Services;
using DateFactoryApp.ViewModels;
using DateFactoryApp.Views;
using DateFactoryApp.Helpers;
using Serilog;
using System.Linq;

namespace DateFactoryApp
{
    public partial class App : Application
    {
        private IServiceProvider? _serviceProvider;

        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            try
            {
                // Configure Serilog
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .WriteTo.File("logs/app-.txt", rollingInterval: RollingInterval.Day)
                    .CreateLogger();

                Log.Information("Application starting...");

                // Configure dependency injection
                var services = new ServiceCollection();
                ConfigureServices(services);
                _serviceProvider = services.BuildServiceProvider();

                Log.Information("Services configured successfully");

                // Initialize database
                await InitializeDatabaseAsync();

                Log.Information("Database initialized successfully");

                // Log system start
                try
                {
                    AuditLogger.LogSystemStart();
                }
                catch (Exception auditEx)
                {
                    Log.Warning(auditEx, "Failed to log system start");
                }

                // Show login window
                try
                {
                    var loginWindow = new LoginWindow();
                    loginWindow.Show();
                    Log.Information("Login window created and shown successfully");
                }
                catch (Exception loginEx)
                {
                    Log.Error(loginEx, "Failed to create or show login window");

                    // Fallback to simple window if login window fails
                    var fallbackWindow = new Window
                    {
                        Title = "نظام إدارة مصنع ومحل التمور",
                        Width = 500,
                        Height = 350,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen,
                        Content = new TextBlock
                        {
                            Text = "مرحباً بك في نظام إدارة مصنع ومحل التمور\n\nحدث خطأ في تحميل نافذة تسجيل الدخول\n\nبيانات الدخول الافتراضية:\nاسم المستخدم: admin\nكلمة المرور: admin123\n\nيرجى إعادة تشغيل التطبيق",
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            FontSize = 14,
                            TextAlignment = TextAlignment.Center,
                            Margin = new Thickness(20)
                        }
                    };
                    fallbackWindow.Show();
                    Log.Information("Fallback window shown due to login window error");
                }

                Log.Information("Login window shown");
            }
            catch (Exception ex)
            {
                var errorDetails = $"خطأ في بدء التطبيق:\n{ex.Message}\n\nالتفاصيل:\n{ex.StackTrace}";

                try
                {
                    Log.Fatal(ex, "Application failed to start");
                }
                catch
                {
                    // If logging fails, ignore it
                }

                MessageBox.Show(errorDetails, "خطأ في بدء التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Database
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite("Data Source=DateFactory.db"));

            // Services
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<ISyncService, SyncService>();
            services.AddScoped<IThemeService, ThemeService>();
            services.AddScoped<ISalesService, SalesService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IProductionService, ProductionService>();
            services.AddScoped<IReportService, ReportService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<ProductsViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<CustomersViewModel>();
            services.AddTransient<SuppliersViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();
            services.AddTransient<ProductionViewModel>();
        }

        public static T GetService<T>() where T : class
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService<T>();
        }

        public static object GetService(Type serviceType)
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService(serviceType);
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                using var scope = _serviceProvider!.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Create default admin user if not exists
                await CreateDefaultUserAsync(context);

                Log.Information("Database initialized successfully");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to initialize database");
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async Task CreateDefaultUserAsync(AppDbContext context)
        {
            try
            {
                var adminExists = await context.Users.AnyAsync(u => u.Username == "admin");
                if (!adminExists)
                {
                    var adminUser = new Models.User
                    {
                        Username = "admin",
                        PasswordHash = "admin", // In real app, this should be hashed
                        FullName = "مدير النظام",
                        Role = Models.UserRole.Manager,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();
                    Log.Information("Default admin user created");
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to create default user");
                // Don't throw here, as this is not critical
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // Log system shutdown
            AuditLogger.LogSystemShutdown();

            _serviceProvider?.GetService<IServiceScope>()?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
