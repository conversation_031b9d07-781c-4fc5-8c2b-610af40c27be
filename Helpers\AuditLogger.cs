using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using DateFactoryApp.Models;

namespace DateFactoryApp.Helpers
{
    public enum AuditAction
    {
        Login,
        Logout,
        Create,
        Update,
        Delete,
        View,
        Export,
        Print,
        Backup,
        Restore,
        Sync,
        PasswordChange,
        SettingsChange,
        SystemStart,
        SystemShutdown
    }

    public class AuditLogEntry
    {
        public int Id { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public AuditAction Action { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public bool IsSuccess { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    public class AuditLogger
    {
        private static AuditLogger? _instance;
        private static readonly object _lock = new object();
        private readonly string _logFilePath;
        private readonly List<AuditLogEntry> _logEntries;

        public static AuditLogger Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new AuditLogger();
                    }
                }
                return _instance;
            }
        }

        private AuditLogger()
        {
            _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "audit.json");
            _logEntries = new List<AuditLogEntry>();
            
            // Ensure log directory exists
            var logDir = Path.GetDirectoryName(_logFilePath);
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir!);
                
            LoadLogs();
        }

        public async Task LogAsync(AuditAction action, string description, string entityType = "", string entityId = "", string details = "", bool isSuccess = true, string errorMessage = "")
        {
            try
            {
                var entry = new AuditLogEntry
                {
                    Id = _logEntries.Count + 1,
                    Timestamp = DateTime.Now,
                    UserId = GetCurrentUserId(),
                    UserName = GetCurrentUserName(),
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    Description = description,
                    Details = details,
                    IpAddress = GetLocalIpAddress(),
                    UserAgent = "DateFactoryApp v1.0",
                    IsSuccess = isSuccess,
                    ErrorMessage = errorMessage
                };

                _logEntries.Add(entry);
                await SaveLogsAsync();

                // Keep only last 1000 entries to prevent file from growing too large
                if (_logEntries.Count > 1000)
                {
                    _logEntries.RemoveRange(0, _logEntries.Count - 1000);
                    await SaveLogsAsync();
                }
            }
            catch (Exception ex)
            {
                // Log to system event log or debug output as fallback
                System.Diagnostics.Debug.WriteLine($"Audit logging failed: {ex.Message}");
            }
        }

        public void Log(AuditAction action, string description, string entityType = "", string entityId = "", string details = "", bool isSuccess = true, string errorMessage = "")
        {
            Task.Run(async () => await LogAsync(action, description, entityType, entityId, details, isSuccess, errorMessage));
        }

        public async Task<List<AuditLogEntry>> GetLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? userId = null, AuditAction? action = null)
        {
            var filteredLogs = new List<AuditLogEntry>(_logEntries);

            if (fromDate.HasValue)
                filteredLogs.RemoveAll(l => l.Timestamp < fromDate.Value);

            if (toDate.HasValue)
                filteredLogs.RemoveAll(l => l.Timestamp > toDate.Value);

            if (!string.IsNullOrEmpty(userId))
                filteredLogs.RemoveAll(l => l.UserId != userId);

            if (action.HasValue)
                filteredLogs.RemoveAll(l => l.Action != action.Value);

            return filteredLogs;
        }

        public async Task<bool> ExportLogsAsync(string filePath, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var logs = await GetLogsAsync(fromDate, toDate);
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(logs, options);
                await File.WriteAllTextAsync(filePath, json);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task ClearLogsAsync()
        {
            _logEntries.Clear();
            await SaveLogsAsync();
        }

        private void LoadLogs()
        {
            try
            {
                if (File.Exists(_logFilePath))
                {
                    var json = File.ReadAllText(_logFilePath);
                    var logs = JsonSerializer.Deserialize<List<AuditLogEntry>>(json);
                    if (logs != null)
                    {
                        _logEntries.AddRange(logs);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading audit logs: {ex.Message}");
            }
        }

        private async Task SaveLogsAsync()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_logEntries, options);
                await File.WriteAllTextAsync(_logFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving audit logs: {ex.Message}");
            }
        }

        private string GetCurrentUserId()
        {
            // TODO: Get from current user service
            return "1";
        }

        private string GetCurrentUserName()
        {
            // TODO: Get from current user service
            return "admin";
        }

        private string GetLocalIpAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
            }
            catch
            {
                // Ignore errors
            }
            return "127.0.0.1";
        }

        // Convenience methods for common actions
        public static void LogLogin(string userName, bool success, string errorMessage = "")
        {
            Instance.Log(AuditAction.Login, $"تسجيل دخول المستخدم: {userName}", "User", userName, "", success, errorMessage);
        }

        public static void LogLogout(string userName)
        {
            Instance.Log(AuditAction.Logout, $"تسجيل خروج المستخدم: {userName}", "User", userName);
        }

        public static void LogCreate(string entityType, string entityId, string description)
        {
            Instance.Log(AuditAction.Create, description, entityType, entityId);
        }

        public static void LogUpdate(string entityType, string entityId, string description)
        {
            Instance.Log(AuditAction.Update, description, entityType, entityId);
        }

        public static void LogDelete(string entityType, string entityId, string description)
        {
            Instance.Log(AuditAction.Delete, description, entityType, entityId);
        }

        public static void LogView(string entityType, string entityId, string description)
        {
            Instance.Log(AuditAction.View, description, entityType, entityId);
        }

        public static void LogExport(string description, string details = "")
        {
            Instance.Log(AuditAction.Export, description, "", "", details);
        }

        public static void LogPrint(string description, string details = "")
        {
            Instance.Log(AuditAction.Print, description, "", "", details);
        }

        public static void LogBackup(string description, bool success, string errorMessage = "")
        {
            Instance.Log(AuditAction.Backup, description, "", "", "", success, errorMessage);
        }

        public static void LogRestore(string description, bool success, string errorMessage = "")
        {
            Instance.Log(AuditAction.Restore, description, "", "", "", success, errorMessage);
        }

        public static void LogSync(string description, bool success, string errorMessage = "")
        {
            Instance.Log(AuditAction.Sync, description, "", "", "", success, errorMessage);
        }

        public static void LogPasswordChange(string userName, bool success, string errorMessage = "")
        {
            Instance.Log(AuditAction.PasswordChange, $"تغيير كلمة مرور المستخدم: {userName}", "User", userName, "", success, errorMessage);
        }

        public static void LogSettingsChange(string description)
        {
            Instance.Log(AuditAction.SettingsChange, description);
        }

        public static void LogSystemStart()
        {
            Instance.Log(AuditAction.SystemStart, "بدء تشغيل النظام");
        }

        public static void LogSystemShutdown()
        {
            Instance.Log(AuditAction.SystemShutdown, "إغلاق النظام");
        }
    }
}
