# دليل المساهمة في نظام إدارة مصنع ومحل التمور

نرحب بمساهماتكم في تطوير وتحسين نظام إدارة مصنع ومحل التمور! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح مميزات جديدة](#اقتراح-مميزات-جديدة)
- [إرشادات التطوير](#إرشادات-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)

## 🤝 كيفية المساهمة

### 1. إعداد البيئة التطويرية

```bash
# استنساخ المشروع
git clone https://github.com/your-repo/DateFactoryApp.git
cd DateFactoryApp

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

### 2. إنشاء فرع جديد

```bash
# إنشاء فرع جديد للميزة أو الإصلاح
git checkout -b feature/اسم-الميزة
# أو
git checkout -b bugfix/وصف-الخطأ
```

### 3. تطوير التغييرات

- اكتب كود نظيف ومفهوم
- أضف تعليقات باللغة العربية
- اتبع معايير الكود المحددة
- اختبر التغييرات جيداً

### 4. إرسال Pull Request

```bash
# إضافة التغييرات
git add .

# إنشاء commit مع رسالة واضحة
git commit -m "إضافة: وصف التغيير"

# رفع التغييرات
git push origin feature/اسم-الميزة
```

## 🐛 الإبلاغ عن الأخطاء

عند العثور على خطأ، يرجى إنشاء Issue جديد مع المعلومات التالية:

### معلومات مطلوبة:
- **وصف الخطأ**: وصف واضح ومفصل للمشكلة
- **خطوات إعادة الإنتاج**: الخطوات المحددة لإعادة إنتاج الخطأ
- **السلوك المتوقع**: ما كان يجب أن يحدث
- **السلوك الفعلي**: ما حدث بالفعل
- **لقطات الشاشة**: إذا كانت مفيدة
- **معلومات البيئة**:
  - نظام التشغيل
  - إصدار .NET
  - إصدار التطبيق

### مثال على تقرير خطأ:

```markdown
## وصف الخطأ
لا يتم حفظ المنتج الجديد عند الضغط على زر "حفظ"

## خطوات إعادة الإنتاج
1. فتح صفحة المنتجات
2. الضغط على "منتج جديد"
3. ملء جميع الحقول المطلوبة
4. الضغط على "حفظ"

## السلوك المتوقع
يجب حفظ المنتج وظهوره في القائمة

## السلوك الفعلي
لا يحدث شيء، ولا يظهر المنتج في القائمة

## معلومات البيئة
- نظام التشغيل: Windows 11
- إصدار .NET: 8.0
- إصدار التطبيق: 1.0.0
```

## 💡 اقتراح مميزات جديدة

لاقتراح ميزة جديدة، يرجى إنشاء Issue مع:

- **وصف الميزة**: وصف مفصل للميزة المقترحة
- **المبرر**: لماذا هذه الميزة مفيدة؟
- **حالات الاستخدام**: أمثلة على كيفية استخدام الميزة
- **التصميم المقترح**: إذا كان لديك أفكار للتصميم

## 🛠️ إرشادات التطوير

### بنية المشروع

```
DateFactoryApp/
├── Models/              # نماذج البيانات
├── ViewModels/          # منطق العرض (MVVM)
├── Views/               # واجهات المستخدم (XAML)
├── Services/            # الخدمات والمنطق التجاري
├── Data/                # قاعدة البيانات والسياق
├── Resources/           # الموارد والأنماط
├── Helpers/             # المساعدات والأدوات
└── Reports/             # قوالب التقارير
```

### التقنيات المستخدمة

- **C# 12**: لغة البرمجة الأساسية
- **WPF**: إطار عمل واجهة المستخدم
- **Material Design**: مكتبة التصميم
- **Entity Framework Core**: ORM
- **SQLite**: قاعدة البيانات
- **MVVM Pattern**: نمط التصميم

## 📝 معايير الكود

### التسمية
- **Classes**: PascalCase (مثل: `ProductService`)
- **Methods**: PascalCase (مثل: `GetAllProducts`)
- **Properties**: PascalCase (مثل: `ProductName`)
- **Fields**: camelCase مع _ (مثل: `_productService`)
- **Variables**: camelCase (مثل: `productList`)

### التعليقات
```csharp
/// <summary>
/// يحصل على جميع المنتجات النشطة من قاعدة البيانات
/// </summary>
/// <returns>قائمة بالمنتجات النشطة</returns>
public async Task<IEnumerable<Product>> GetActiveProductsAsync()
{
    // تصفية المنتجات النشطة فقط
    return await _context.Products
        .Where(p => p.IsActive && !p.IsDeleted)
        .ToListAsync();
}
```

### معالجة الأخطاء
```csharp
try
{
    // الكود الرئيسي
    await _service.SaveProductAsync(product);
}
catch (Exception ex)
{
    // تسجيل الخطأ
    _logger.LogError(ex, "خطأ في حفظ المنتج {ProductId}", product.Id);
    
    // إظهار رسالة للمستخدم
    MessageBox.Show($"حدث خطأ أثناء حفظ المنتج: {ex.Message}", 
        "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
}
```

### XAML
```xml
<!-- استخدام أسماء واضحة للعناصر -->
<TextBox x:Name="ProductNameTextBox"
         materialDesign:HintAssist.Hint="اسم المنتج"
         Text="{Binding ProductName}"
         Style="{StaticResource FormTextBox}"/>
```

## 🔍 عملية المراجعة

### قبل إرسال Pull Request
- [ ] تأكد من أن الكود يعمل بشكل صحيح
- [ ] اختبر جميع الوظائف المتأثرة
- [ ] تأكد من عدم وجود أخطاء في البناء
- [ ] اتبع معايير الكود المحددة
- [ ] أضف تعليقات مناسبة

### مراجعة الكود
سيتم مراجعة جميع Pull Requests من قبل فريق التطوير للتأكد من:

- **جودة الكود**: اتباع المعايير والأفضل الممارسات
- **الوظائف**: عمل الميزات كما هو متوقع
- **الأداء**: عدم تأثير التغييرات على الأداء
- **الأمان**: عدم وجود ثغرات أمنية
- **التوافق**: التوافق مع الكود الموجود

## 🎯 أولويات التطوير

### عالية الأولوية
- إصلاح الأخطاء الحرجة
- تحسينات الأمان
- تحسينات الأداء

### متوسطة الأولوية
- مميزات جديدة مطلوبة
- تحسينات واجهة المستخدم
- تحسينات قابلية الاستخدام

### منخفضة الأولوية
- تحسينات الكود
- تحديث الوثائق
- مميزات إضافية

## 📞 التواصل

للأسئلة والاستفسارات:
- إنشاء Issue في GitHub
- مراسلة فريق التطوير
- مراجعة الوثائق الموجودة

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT License).

---

**شكراً لمساهمتكم في تطوير نظام إدارة مصنع ومحل التمور!** 🙏
