<UserControl x:Class="DateFactoryApp.Views.ProductsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة المنتجات" Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search and Filters -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox x:Name="SearchTextBox" Width="300" 
                           materialDesign:HintAssist.Hint="البحث في المنتجات..."
                           Style="{StaticResource FormTextBox}"
                           Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                    
                    <Button Content="بحث" Style="{StaticResource SecondaryButton}"
                          Command="{Binding SearchCommand}" Margin="8,0"/>
                    
                    <Button Content="مخزون منخفض" Style="{StaticResource WarningButton}"
                          Command="{Binding ShowLowStockCommand}" Margin="8,0"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="منتج جديد" Style="{StaticResource PrimaryButton}"
                          Command="{Binding AddProductCommand}">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                    
                    <Button Content="تصنيفات" Style="{StaticResource SecondaryButton}"
                          Command="{Binding ManageCategoriesCommand}" Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Products List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة المنتجات" Style="{StaticResource SectionTitle}" Margin="16,16,16,8"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Products}" 
                            SelectedItem="{Binding SelectedProduct}"
                            Style="{StaticResource CustomDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="100"/>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                            <DataGridTextColumn Header="التصنيف" Binding="{Binding Category.Name}" Width="120"/>
                            <DataGridTextColumn Header="المخزون" Binding="{Binding CurrentStock}" Width="80"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="60"/>
                            <DataGridTextColumn Header="سعر الشراء" Binding="{Binding PurchasePrice, StringFormat='{}{0:C}'}" Width="100"/>
                            <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice, StringFormat='{}{0:C}'}" Width="100"/>
                            <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.EditProductCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="تعديل">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Command="{Binding DataContext.DeleteProductCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  ToolTip="حذف">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Product Details/Edit Form -->
            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" 
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Style="{StaticResource FormPanel}">
                        <TextBlock Text="{Binding EditingProduct.Id, Converter={StaticResource ProductFormTitleConverter}}" 
                                 Style="{StaticResource SectionTitle}"/>

                        <TextBox materialDesign:HintAssist.Hint="اسم المنتج *"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.Name}"/>

                        <TextBox materialDesign:HintAssist.Hint="كود المنتج"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.Code}"/>

                        <TextBox materialDesign:HintAssist.Hint="الوصف"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.Description}"
                               AcceptsReturn="True" MaxLines="3"/>

                        <ComboBox materialDesign:HintAssist.Hint="التصنيف *"
                                Style="{StaticResource FormComboBox}"
                                ItemsSource="{Binding Categories}"
                                SelectedValue="{Binding EditingProduct.CategoryId}"
                                SelectedValuePath="Id"
                                DisplayMemberPath="Name"/>

                        <ComboBox materialDesign:HintAssist.Hint="نوع المنتج"
                                Style="{StaticResource FormComboBox}"
                                SelectedItem="{Binding EditingProduct.Type}"/>

                        <ComboBox materialDesign:HintAssist.Hint="الجودة"
                                Style="{StaticResource FormComboBox}"
                                SelectedItem="{Binding EditingProduct.Quality}"/>

                        <ComboBox materialDesign:HintAssist.Hint="حجم العبوة"
                                Style="{StaticResource FormComboBox}"
                                SelectedItem="{Binding EditingProduct.PackageSize}"/>

                        <TextBox materialDesign:HintAssist.Hint="سعر الشراء"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.PurchasePrice}"/>

                        <TextBox materialDesign:HintAssist.Hint="سعر البيع"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.SalePrice}"/>

                        <TextBox materialDesign:HintAssist.Hint="سعر الجملة"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.WholesalePrice}"/>

                        <TextBox materialDesign:HintAssist.Hint="الحد الأدنى للمخزون"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.MinStockLevel}"/>

                        <TextBox materialDesign:HintAssist.Hint="الحد الأقصى للمخزون"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.MaxStockLevel}"/>

                        <TextBox materialDesign:HintAssist.Hint="الوحدة"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.Unit}"/>

                        <TextBox materialDesign:HintAssist.Hint="الباركود"
                               Style="{StaticResource FormTextBox}"
                               Text="{Binding EditingProduct.Barcode}"/>

                        <DatePicker materialDesign:HintAssist.Hint="تاريخ الانتهاء"
                                  Style="{StaticResource FormDatePicker}"
                                  SelectedDate="{Binding EditingProduct.ExpiryDate}"/>

                        <CheckBox Content="منتج نشط" Margin="0,16,0,8"
                                IsChecked="{Binding EditingProduct.IsActive}"/>

                        <StackPanel Style="{StaticResource ButtonPanel}">
                            <Button Content="حفظ" Style="{StaticResource SuccessButton}"
                                  Command="{Binding SaveProductCommand}"/>
                            <Button Content="إلغاء" Style="{StaticResource SecondaryButton}"
                                  Command="{Binding CancelEditCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" Background="Black" Opacity="0.5" 
            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="50" Height="50" IsIndeterminate="True"/>
                <TextBlock Text="جاري التحميل..." Foreground="White" HorizontalAlignment="Center" Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
