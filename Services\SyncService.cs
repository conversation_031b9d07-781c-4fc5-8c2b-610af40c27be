using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;
using Newtonsoft.Json;
using Serilog;

namespace DateFactoryApp.Services
{
    public class SyncService : ISyncService
    {
        private readonly AppDbContext _context;
        private readonly HttpClient _httpClient;
        private string _syncEndpoint = "";
        private string _apiKey = "";
        private bool _autoSyncEnabled = false;
        private System.Threading.Timer? _autoSyncTimer;

        public event EventHandler<SyncProgressEventArgs>? SyncProgressChanged;

        public SyncService(AppDbContext context)
        {
            _context = context;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(5);

            LoadSyncSettings();
            InitializeAutoSync();
        }

        public async Task<bool> SyncToCloudAsync()
        {
            try
            {
                if (!await IsOnlineAsync())
                {
                    Log.Warning("Cannot sync: No internet connection");
                    return false;
                }

                if (string.IsNullOrEmpty(_syncEndpoint) || string.IsNullOrEmpty(_apiKey))
                {
                    Log.Warning("Cannot sync: Sync endpoint or API key not configured");
                    return false;
                }

                var pendingItems = await GetPendingItemsAsync();
                var totalItems = pendingItems.Count;
                var processedItems = 0;

                OnSyncProgressChanged(new SyncProgressEventArgs
                {
                    TotalItems = totalItems,
                    ProcessedItems = processedItems,
                    CurrentOperation = "بدء المزامنة..."
                });

                foreach (var item in pendingItems)
                {
                    try
                    {
                        await SyncItemToCloudAsync(item);
                        processedItems++;

                        OnSyncProgressChanged(new SyncProgressEventArgs
                        {
                            TotalItems = totalItems,
                            ProcessedItems = processedItems,
                            CurrentOperation = $"مزامنة {item.GetType().Name}..."
                        });
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Failed to sync item {ItemType} with ID {ItemId}", item.GetType().Name, item.Id);
                    }
                }

                await UpdateLastSyncTimeAsync();
                Log.Information("Sync to cloud completed. {ProcessedItems}/{TotalItems} items synced", processedItems, totalItems);

                return processedItems == totalItems;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to sync to cloud");
                return false;
            }
        }

        public async Task<bool> SyncFromCloudAsync()
        {
            try
            {
                if (!await IsOnlineAsync())
                {
                    Log.Warning("Cannot sync: No internet connection");
                    return false;
                }

                if (string.IsNullOrEmpty(_syncEndpoint) || string.IsNullOrEmpty(_apiKey))
                {
                    Log.Warning("Cannot sync: Sync endpoint or API key not configured");
                    return false;
                }

                OnSyncProgressChanged(new SyncProgressEventArgs
                {
                    TotalItems = 1,
                    ProcessedItems = 0,
                    CurrentOperation = "جلب البيانات من السحابة..."
                });

                var lastSyncTime = await GetLastSyncTimeAsync();
                var cloudData = await GetCloudDataAsync(lastSyncTime);

                if (cloudData != null)
                {
                    await ProcessCloudDataAsync(cloudData);
                    await UpdateLastSyncTimeAsync();

                    OnSyncProgressChanged(new SyncProgressEventArgs
                    {
                        TotalItems = 1,
                        ProcessedItems = 1,
                        CurrentOperation = "اكتملت المزامنة"
                    });

                    Log.Information("Sync from cloud completed successfully");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to sync from cloud");
                return false;
            }
        }

        public async Task<bool> IsOnlineAsync()
        {
            try
            {
                using var response = await _httpClient.GetAsync("https://www.google.com", HttpCompletionOption.ResponseHeadersRead);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<SyncStatus> GetSyncStatusAsync()
        {
            return new SyncStatus
            {
                IsOnline = await IsOnlineAsync(),
                LastSyncTime = await GetLastSyncTimeAsync(),
                PendingChanges = await GetPendingSyncCountAsync(),
                AutoSyncEnabled = _autoSyncEnabled,
                State = await IsOnlineAsync() ? SyncState.Idle : SyncState.Offline
            };
        }

        public async Task<bool> EnableAutoSyncAsync(bool enable)
        {
            _autoSyncEnabled = enable;
            await SaveSyncSettingsAsync();

            if (enable)
            {
                InitializeAutoSync();
            }
            else
            {
                _autoSyncTimer?.Dispose();
                _autoSyncTimer = null;
            }

            return true;
        }

        public async Task<bool> IsAutoSyncEnabledAsync()
        {
            return _autoSyncEnabled;
        }

        public async Task<DateTime?> GetLastSyncTimeAsync()
        {
            // This would typically be stored in a settings table or configuration file
            // For now, we'll use a simple approach
            var settingsPath = "sync_settings.json";
            if (File.Exists(settingsPath))
            {
                try
                {
                    var json = await File.ReadAllTextAsync(settingsPath);
                    var settings = JsonConvert.DeserializeObject<SyncSettings>(json);
                    return settings?.LastSyncTime;
                }
                catch
                {
                    return null;
                }
            }
            return null;
        }

        public async Task<int> GetPendingSyncCountAsync()
        {
            var count = 0;

            count += await _context.Products.CountAsync(p => !p.IsSynced && !p.IsDeleted);
            count += await _context.Sales.CountAsync(s => !s.IsSynced && !s.IsDeleted);
            count += await _context.Customers.CountAsync(c => !c.IsSynced && !c.IsDeleted);
            count += await _context.Suppliers.CountAsync(s => !s.IsSynced && !s.IsDeleted);
            count += await _context.InventoryTransactions.CountAsync(t => !t.IsSynced && !t.IsDeleted);

            return count;
        }

        public async Task<bool> ConfigureSyncEndpointAsync(string endpoint, string apiKey)
        {
            _syncEndpoint = endpoint;
            _apiKey = apiKey;
            await SaveSyncSettingsAsync();
            return true;
        }

        private async Task<List<BaseEntity>> GetPendingItemsAsync()
        {
            var items = new List<BaseEntity>();

            items.AddRange(await _context.Products.Where(p => !p.IsSynced && !p.IsDeleted).ToListAsync());
            items.AddRange(await _context.Sales.Where(s => !s.IsSynced && !s.IsDeleted).ToListAsync());
            items.AddRange(await _context.Customers.Where(c => !c.IsSynced && !c.IsDeleted).ToListAsync());
            items.AddRange(await _context.Suppliers.Where(s => !s.IsSynced && !s.IsDeleted).ToListAsync());
            items.AddRange(await _context.InventoryTransactions.Where(t => !t.IsSynced && !t.IsDeleted).ToListAsync());

            return items.OrderBy(i => i.CreatedAt).ToList();
        }

        private async Task<bool> SyncItemToCloudAsync(BaseEntity item)
        {
            try
            {
                var json = JsonConvert.SerializeObject(item);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                var endpoint = $"{_syncEndpoint}/sync/{item.GetType().Name.ToLower()}";
                var response = await _httpClient.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    item.IsSynced = true;
                    item.LastSyncedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to sync item to cloud");
                return false;
            }
        }

        private async Task<CloudData?> GetCloudDataAsync(DateTime? lastSyncTime)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                var endpoint = $"{_syncEndpoint}/sync/changes";
                if (lastSyncTime.HasValue)
                {
                    endpoint += $"?since={lastSyncTime.Value:yyyy-MM-ddTHH:mm:ssZ}";
                }

                var response = await _httpClient.GetAsync(endpoint);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<CloudData>(json);
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to get cloud data");
                return null;
            }
        }

        private async Task ProcessCloudDataAsync(CloudData cloudData)
        {
            // Process cloud data and update local database
            // This is a simplified implementation
            // In a real scenario, you'd need to handle conflicts, deletions, etc.

            foreach (var product in cloudData.Products ?? new List<Product>())
            {
                var existingProduct = await _context.Products.FindAsync(product.Id);
                if (existingProduct == null)
                {
                    _context.Products.Add(product);
                }
                else if (product.UpdatedAt > existingProduct.UpdatedAt)
                {
                    _context.Entry(existingProduct).CurrentValues.SetValues(product);
                }
            }

            // Similar processing for other entities...

            await _context.SaveChangesAsync();
        }

        private async Task UpdateLastSyncTimeAsync()
        {
            var settings = new SyncSettings
            {
                LastSyncTime = DateTime.Now,
                SyncEndpoint = _syncEndpoint,
                ApiKey = _apiKey,
                AutoSyncEnabled = _autoSyncEnabled
            };

            var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
            await File.WriteAllTextAsync("sync_settings.json", json);
        }

        private async Task SaveSyncSettingsAsync()
        {
            var settings = new SyncSettings
            {
                LastSyncTime = await GetLastSyncTimeAsync(),
                SyncEndpoint = _syncEndpoint,
                ApiKey = _apiKey,
                AutoSyncEnabled = _autoSyncEnabled
            };

            var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
            await File.WriteAllTextAsync("sync_settings.json", json);
        }

        private void LoadSyncSettings()
        {
            try
            {
                var settingsPath = "sync_settings.json";
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    var settings = JsonConvert.DeserializeObject<SyncSettings>(json);
                    if (settings != null)
                    {
                        _syncEndpoint = settings.SyncEndpoint ?? "";
                        _apiKey = settings.ApiKey ?? "";
                        _autoSyncEnabled = settings.AutoSyncEnabled;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to load sync settings");
            }
        }

        private void InitializeAutoSync()
        {
            if (_autoSyncEnabled)
            {
                _autoSyncTimer?.Dispose();
                _autoSyncTimer = new System.Threading.Timer(async _ => await AutoSyncCallback(), null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(15));
            }
        }

        private async Task AutoSyncCallback()
        {
            try
            {
                if (await IsOnlineAsync() && await GetPendingSyncCountAsync() > 0)
                {
                    await SyncToCloudAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Auto sync failed");
            }
        }

        private void OnSyncProgressChanged(SyncProgressEventArgs e)
        {
            SyncProgressChanged?.Invoke(this, e);
        }

        public void Dispose()
        {
            _autoSyncTimer?.Dispose();
            _httpClient?.Dispose();
        }
    }

    public class SyncSettings
    {
        public DateTime? LastSyncTime { get; set; }
        public string? SyncEndpoint { get; set; }
        public string? ApiKey { get; set; }
        public bool AutoSyncEnabled { get; set; }
    }

    public class CloudData
    {
        public List<Product>? Products { get; set; }
        public List<Sale>? Sales { get; set; }
        public List<Customer>? Customers { get; set; }
        public List<Supplier>? Suppliers { get; set; }
        public List<InventoryTransaction>? InventoryTransactions { get; set; }
    }
}
