<UserControl x:Class="DateFactoryApp.Views.ProductionView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" Text="إدارة الإنتاج" Style="{StaticResource PageTitle}"/>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" Margin="0,16,0,0">
            <StackPanel Margin="32" HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="Factory" Width="64" Height="64" 
                                       Foreground="{StaticResource PrimaryBrush}" 
                                       HorizontalAlignment="Center" Margin="0,0,0,16"/>
                
                <TextBlock Text="قسم الإنتاج" Style="{StaticResource SectionTitle}" 
                         HorizontalAlignment="Center" Margin="0,0,0,8"/>
                
                <TextBlock Text="هذا القسم قيد التطوير" 
                         Style="{StaticResource BodyText}" 
                         HorizontalAlignment="Center" Margin="0,0,0,16"/>
                
                <TextBlock Text="سيتم إضافة الميزات التالية قريباً:" 
                         FontWeight="Medium" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="• إدارة أوامر الإنتاج" Margin="0,2"/>
                    <TextBlock Text="• تتبع مراحل الإنتاج" Margin="0,2"/>
                    <TextBlock Text="• إدارة المواد الخام" Margin="0,2"/>
                    <TextBlock Text="• تقارير الإنتاج" Margin="0,2"/>
                    <TextBlock Text="• مراقبة الجودة" Margin="0,2"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
