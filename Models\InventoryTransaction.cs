using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class InventoryTransaction : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string TransactionNumber { get; set; } = string.Empty;

        [Required]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        public int ProductId { get; set; }

        [Required]
        public TransactionType Type { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal StockBefore { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal StockAfter { get; set; }

        [MaxLength(500)]
        public string? Notes { get; set; }

        [MaxLength(100)]
        public string? Reference { get; set; }

        public int? SupplierId { get; set; }

        public int? UserId { get; set; }

        public int? SaleId { get; set; }

        public int? ProductionRecordId { get; set; }

        // Navigation properties
        public virtual Product Product { get; set; } = null!;
        public virtual Supplier? Supplier { get; set; }
        public virtual User? User { get; set; }
        public virtual Sale? Sale { get; set; }
        public virtual ProductionRecord? ProductionRecord { get; set; }
    }

    public enum TransactionType
    {
        Purchase = 1,       // شراء
        Sale = 2,           // بيع
        Production = 3,     // إنتاج
        Adjustment = 4,     // تسوية
        Transfer = 5,       // نقل
        Return = 6,         // إرجاع
        Damage = 7,         // تالف
        Expired = 8         // منتهي الصلاحية
    }
}
